"""
Configuration file for Tunisian ID Card OCR and Translation System
"""

import os
from pathlib import Path

# Paths configuration
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "data"
OUTPUT_DIR = BASE_DIR / "output"
ANALYSIS_OUTPUT_DIR = BASE_DIR / "analysis_output"
TEMP_DIR = BASE_DIR / "temp"

# Create directories if they don't exist
for directory in [OUTPUT_DIR, ANALYSIS_OUTPUT_DIR, TEMP_DIR]:
    directory.mkdir(exist_ok=True)

# OCR Configuration
OCR_CONFIG = {
    # Tesseract configuration for Arabic
    'tesseract': {
        'lang': 'ara+eng',  # Arabic + English
        'config': '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyzأبتثجحخدذرزسشصضطظعغفقكلمنهويءآإؤئةى'
    },
    
    # EasyOCR configuration
    'easyocr': {
        'languages': ['ar', 'en'],  # Arabic and English
        'gpu': False,  # Set to True if you have CUDA GPU
        'detail': 1,  # Return bounding box coordinates
        'paragraph': False,
        'width_ths': 0.7,
        'height_ths': 0.7,
        'decoder': 'greedy'
    },
    
    # Image preprocessing parameters
    'preprocessing': {
        'resize_factor': 2.0,
        'gaussian_blur_kernel': (5, 5),
        'bilateral_filter': {'d': 9, 'sigmaColor': 75, 'sigmaSpace': 75},
        'morphology_kernel_size': (3, 3),
        'contrast_alpha': 1.5,
        'brightness_beta': 30
    }
}

# Translation Configuration
TRANSLATION_CONFIG = {
    'model_name': 'Helsinki-NLP/opus-mt-ar-fr',  # Arabic to French model
    'backup_model': 'Helsinki-NLP/opus-mt-ar-en',  # Arabic to English backup
    'max_length': 512,
    'temperature': 0.1,
    'do_sample': False
}

# ID Card Field Patterns (Arabic and French)
ID_CARD_PATTERNS = {
    'card_number': {
        'patterns': [
            r'رقم البطاقة[:\s]*(\d{8})',
            r'N°[:\s]*(\d{8})',
            r'(\d{8})',
            r'(\d{7,9})',  # More flexible number pattern
        ],
        'field_name': 'card_number'
    },
    'first_name': {
        'patterns': [
            r'الاسم[:\s]*([أ-ي\s]+)',
            r'Prénom[:\s]*([A-Za-z\s]+)',
            r'الاسم الشخصي[:\s]*([أ-ي\s]+)',
            r'اسم[:\s]*([أ-ي\s]+)',  # Simplified pattern
        ],
        'field_name': 'first_name'
    },
    'last_name': {
        'patterns': [
            r'اللقب[:\s]*([أ-ي\s]+)',
            r'Nom[:\s]*([A-Za-z\s]+)',
            r'اسم العائلة[:\s]*([أ-ي\s]+)',
            r'لقب[:\s]*([أ-ي\s]+)',  # Simplified pattern
        ],
        'field_name': 'last_name'
    },
    'birth_date': {
        'patterns': [
            r'تاريخ الولادة[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            r'Date de naissance[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            r'(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            r'(\d{1,2}\.\d{1,2}\.\d{4})',  # Dot separator
            r'(\d{4}[/-]\d{1,2}[/-]\d{1,2})',  # Year first format
        ],
        'field_name': 'birth_date'
    },
    'birth_place': {
        'patterns': [
            r'مكان الولادة[:\s]*([أ-ي\s]+)',
            r'Lieu de naissance[:\s]*([A-Za-z\s]+)',
            r'محل الولادة[:\s]*([أ-ي\s]+)',
            r'مكان[:\s]*([أ-ي\s]+)',  # Simplified pattern
        ],
        'field_name': 'birth_place'
    },
    'nationality': {
        'patterns': [
            r'الجنسية[:\s]*([أ-ي\s]+)',
            r'Nationalité[:\s]*([A-Za-z\s]+)',
            r'جنسية[:\s]*([أ-ي\s]+)',  # Simplified pattern
            r'تونسي',  # Direct match for Tunisian
            r'تونسية',  # Direct match for Tunisian (female)
        ],
        'field_name': 'nationality'
    },
    'address': {
        'patterns': [
            r'العنوان[:\s]*([أ-ي\s\d,.-]+)',
            r'Adresse[:\s]*([A-Za-z\s\d,.-]+)',
            r'محل الإقامة[:\s]*([أ-ي\s\d,.-]+)',
            r'عنوان[:\s]*([أ-ي\s\d,.-]+)',  # Simplified pattern
        ],
        'field_name': 'address'
    },
    'document_type': {
        'patterns': [
            r'بطاقة التعريف',
            r'بطاقه التعريف',
            r'الجمهورية التونسية',
            r'الجمهوريةالقونسي',  # OCR might merge words
            r'République Tunisienne',
            r'Carte d\'identité',
        ],
        'field_name': 'document_type'
    }
}

# Output Configuration
OUTPUT_CONFIG = {
    'formats': ['json', 'csv', 'excel'],
    'json_indent': 2,
    'csv_delimiter': ',',
    'excel_sheet_name': 'ID_Cards_Data',
    'include_confidence_scores': True,
    'include_bounding_boxes': True
}

# Logging Configuration
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': 'id_card_extraction.log'
}

# Quality thresholds
QUALITY_THRESHOLDS = {
    'min_confidence': 0.6,
    'min_text_length': 2,
    'max_text_length': 100,
    'required_fields': ['card_number', 'first_name', 'last_name']
}
