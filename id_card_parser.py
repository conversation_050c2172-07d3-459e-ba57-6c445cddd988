"""
ID Card Parser module for Tunisian ID Cards
Specialized parser to extract and structure information from Tunisian ID cards
"""

import re
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import unicodedata

from config import ID_CARD_PATTERNS, QUALITY_THRESHOLDS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TunisianIDCardParser:
    """Parser specialized for Tunisian ID card information extraction"""
    
    def __init__(self):
        """Initialize the parser with patterns and configurations"""
        self.patterns = ID_CARD_PATTERNS
        self.quality_thresholds = QUALITY_THRESHOLDS
        
        # Arabic to Latin number mapping
        self.arabic_to_latin_numbers = {
            '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
            '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'
        }
        
        # Common Arabic words that might appear on ID cards
        self.arabic_keywords = {
            'رقم البطاقة': 'card_number',
            'الاسم': 'first_name',
            'اللقب': 'last_name',
            'تاريخ الولادة': 'birth_date',
            'مكان الولادة': 'birth_place',
            'الجنسية': 'nationality',
            'العنوان': 'address',
            'محل الإقامة': 'address',
            'اسم العائلة': 'last_name',
            'الاسم الشخصي': 'first_name',
            'محل الولادة': 'birth_place'
        }
        
        # French keywords
        self.french_keywords = {
            'N°': 'card_number',
            'Numéro': 'card_number',
            'Prénom': 'first_name',
            'Nom': 'last_name',
            'Date de naissance': 'birth_date',
            'Lieu de naissance': 'birth_place',
            'Nationalité': 'nationality',
            'Adresse': 'address'
        }
    
    def normalize_arabic_text(self, text: str) -> str:
        """Normalize Arabic text for better processing"""
        if not text:
            return ""
        
        # Remove diacritics
        text = ''.join(c for c in unicodedata.normalize('NFD', text) 
                      if unicodedata.category(c) != 'Mn')
        
        # Convert Arabic numbers to Latin
        for arabic, latin in self.arabic_to_latin_numbers.items():
            text = text.replace(arabic, latin)
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def extract_card_number(self, ocr_results: List[Dict]) -> Optional[Dict]:
        """Extract card number from OCR results"""
        for result in ocr_results:
            text = self.normalize_arabic_text(result['text'])

            # Try different patterns for card number
            for pattern in self.patterns['card_number']['patterns']:
                match = re.search(pattern, text, re.UNICODE)
                if match:
                    card_number = match.group(1) if match.groups() else match.group(0)
                    # Validate card number (should be 7-9 digits, more flexible)
                    if re.match(r'^\d{7,9}$', card_number):
                        return {
                            'value': card_number,
                            'confidence': result['confidence'],
                            'bbox': result['bbox'],
                            'source_text': result['text']
                        }

        # If no pattern match, look for standalone numbers (7-9 digits)
        for result in ocr_results:
            text = self.normalize_arabic_text(result['text'])
            numbers = re.findall(r'\d{7,9}', text)
            if numbers:
                return {
                    'value': numbers[0],
                    'confidence': result['confidence'],
                    'bbox': result['bbox'],
                    'source_text': result['text']
                }

        return None

    def extract_document_type(self, ocr_results: List[Dict]) -> Optional[Dict]:
        """Extract document type to confirm this is a Tunisian ID card"""
        for result in ocr_results:
            text = result['text']

            # Check for document type patterns
            for pattern in self.patterns['document_type']['patterns']:
                if re.search(pattern, text, re.UNICODE):
                    return {
                        'value': 'Tunisian ID Card',
                        'confidence': result['confidence'],
                        'bbox': result['bbox'],
                        'source_text': result['text']
                    }

        return None

    def extract_name_field(self, ocr_results: List[Dict], field_type: str) -> Optional[Dict]:
        """Extract name fields (first_name, last_name) from OCR results"""
        patterns = self.patterns[field_type]['patterns']
        
        for result in ocr_results:
            text = result['text']
            normalized_text = self.normalize_arabic_text(text)
            
            # Try pattern matching
            for pattern in patterns:
                match = re.search(pattern, text, re.UNICODE)
                if match:
                    name = match.group(1).strip()
                    if self._is_valid_name(name):
                        return {
                            'value': name,
                            'confidence': result['confidence'],
                            'bbox': result['bbox'],
                            'source_text': result['text']
                        }
        
        # If no pattern match, look for names near keywords
        return self._extract_name_by_proximity(ocr_results, field_type)
    
    def _extract_name_by_proximity(self, ocr_results: List[Dict], field_type: str) -> Optional[Dict]:
        """Extract names by finding text near relevant keywords"""
        keywords = []
        if field_type == 'first_name':
            keywords = ['الاسم', 'Prénom', 'الاسم الشخصي']
        elif field_type == 'last_name':
            keywords = ['اللقب', 'Nom', 'اسم العائلة']
        
        # Find keyword positions
        keyword_positions = []
        for i, result in enumerate(ocr_results):
            for keyword in keywords:
                if keyword in result['text']:
                    keyword_positions.append((i, result['bbox']))
        
        # Look for names near keywords
        for keyword_idx, keyword_bbox in keyword_positions:
            for i, result in enumerate(ocr_results):
                if i != keyword_idx:
                    distance = self._calculate_bbox_distance(keyword_bbox, result['bbox'])
                    if distance < 100:  # Adjust threshold as needed
                        name = self.normalize_arabic_text(result['text'])
                        if self._is_valid_name(name):
                            return {
                                'value': name,
                                'confidence': result['confidence'],
                                'bbox': result['bbox'],
                                'source_text': result['text']
                            }
        
        return None
    
    def extract_birth_date(self, ocr_results: List[Dict]) -> Optional[Dict]:
        """Extract birth date from OCR results"""
        date_patterns = [
            r'(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            r'(\d{1,2}\.\d{1,2}\.\d{4})',
            r'(\d{4}[/-]\d{1,2}[/-]\d{1,2})'
        ]
        
        for result in ocr_results:
            text = self.normalize_arabic_text(result['text'])
            
            # Try specific birth date patterns first
            for pattern in self.patterns['birth_date']['patterns']:
                match = re.search(pattern, text, re.UNICODE)
                if match:
                    date_str = match.group(1)
                    if self._is_valid_date(date_str):
                        return {
                            'value': self._normalize_date(date_str),
                            'confidence': result['confidence'],
                            'bbox': result['bbox'],
                            'source_text': result['text']
                        }
            
            # Try general date patterns
            for pattern in date_patterns:
                match = re.search(pattern, text)
                if match:
                    date_str = match.group(1)
                    if self._is_valid_date(date_str):
                        return {
                            'value': self._normalize_date(date_str),
                            'confidence': result['confidence'],
                            'bbox': result['bbox'],
                            'source_text': result['text']
                        }
        
        return None
    
    def extract_place_field(self, ocr_results: List[Dict], field_type: str) -> Optional[Dict]:
        """Extract place fields (birth_place, address, nationality) from OCR results"""
        patterns = self.patterns[field_type]['patterns']

        for result in ocr_results:
            text = result['text']

            for pattern in patterns:
                match = re.search(pattern, text, re.UNICODE)
                if match:
                    # Handle patterns with and without groups
                    if match.groups():
                        place = match.group(1).strip()
                    else:
                        place = match.group(0).strip()

                    if len(place) > 1:  # Basic validation
                        return {
                            'value': place,
                            'confidence': result['confidence'],
                            'bbox': result['bbox'],
                            'source_text': result['text']
                        }

        return None
    
    def _is_valid_name(self, name: str) -> bool:
        """Validate if a string is a valid name"""
        if not name or len(name) < 2:
            return False
        
        # Check if it's mostly letters (Arabic or Latin)
        letter_count = sum(1 for c in name if c.isalpha() or c in 'أبتثجحخدذرزسشصضطظعغفقكلمنهويءآإؤئةى')
        return letter_count / len(name.replace(' ', '')) > 0.7
    
    def _is_valid_date(self, date_str: str) -> bool:
        """Validate if a string is a valid date"""
        date_formats = ['%d/%m/%Y', '%d-%m-%Y', '%d.%m.%Y', '%Y/%m/%d', '%Y-%m-%d']
        
        for fmt in date_formats:
            try:
                datetime.strptime(date_str, fmt)
                return True
            except ValueError:
                continue
        return False
    
    def _normalize_date(self, date_str: str) -> str:
        """Normalize date to DD/MM/YYYY format"""
        date_formats = ['%d/%m/%Y', '%d-%m-%Y', '%d.%m.%Y', '%Y/%m/%d', '%Y-%m-%d']
        
        for fmt in date_formats:
            try:
                date_obj = datetime.strptime(date_str, fmt)
                return date_obj.strftime('%d/%m/%Y')
            except ValueError:
                continue
        return date_str
    
    def _calculate_bbox_distance(self, bbox1: List[int], bbox2: List[int]) -> float:
        """Calculate distance between two bounding boxes"""
        x1_center = (bbox1[0] + bbox1[2]) / 2
        y1_center = (bbox1[1] + bbox1[3]) / 2
        x2_center = (bbox2[0] + bbox2[2]) / 2
        y2_center = (bbox2[1] + bbox2[3]) / 2
        
        return ((x1_center - x2_center) ** 2 + (y1_center - y2_center) ** 2) ** 0.5
    
    def parse_id_card(self, ocr_results: List[Dict]) -> Dict:
        """
        Main method to parse all information from OCR results
        """
        logger.info("Starting ID card parsing")
        
        parsed_data = {
            'document_type': None,
            'card_number': None,
            'first_name': None,
            'last_name': None,
            'birth_date': None,
            'birth_place': None,
            'nationality': None,
            'address': None,
            'extraction_metadata': {
                'total_ocr_elements': len(ocr_results),
                'parsing_timestamp': datetime.now().isoformat(),
                'confidence_scores': {},
                'raw_ocr_texts': [r['text'] for r in ocr_results]  # Add raw texts for debugging
            }
        }
        
        # Extract each field
        parsed_data['document_type'] = self.extract_document_type(ocr_results)
        parsed_data['card_number'] = self.extract_card_number(ocr_results)
        parsed_data['first_name'] = self.extract_name_field(ocr_results, 'first_name')
        parsed_data['last_name'] = self.extract_name_field(ocr_results, 'last_name')
        parsed_data['birth_date'] = self.extract_birth_date(ocr_results)
        parsed_data['birth_place'] = self.extract_place_field(ocr_results, 'birth_place')
        parsed_data['nationality'] = self.extract_place_field(ocr_results, 'nationality')
        parsed_data['address'] = self.extract_place_field(ocr_results, 'address')
        
        # Calculate confidence scores
        for field, data in parsed_data.items():
            if isinstance(data, dict) and 'confidence' in data:
                parsed_data['extraction_metadata']['confidence_scores'][field] = data['confidence']
        
        # Calculate overall quality score
        required_fields = self.quality_thresholds['required_fields']
        found_required = sum(1 for field in required_fields if parsed_data[field] is not None)
        quality_score = found_required / len(required_fields)
        parsed_data['extraction_metadata']['quality_score'] = quality_score
        
        logger.info(f"ID card parsing completed. Quality score: {quality_score:.2f}")
        return parsed_data
