"""
Arabic to French Translator module
Uses transformer models for high-quality translation of personal information
"""

import logging
import re
from typing import Dict, Optional, List
import unicodedata

try:
    from transformers import <PERSON><PERSON>Mode<PERSON>, MarianTokenizer, pipeline
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    logging.warning("Transformers library not available. Translation will use fallback methods.")

try:
    from googletrans import Translator as GoogleTranslator
    GOOGLETRANS_AVAILABLE = True
except ImportError:
    GOOGLETRANS_AVAILABLE = False
    logging.warning("Google Translate library not available.")

from config import TRANSLATION_CONFIG

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ArabicTranslator:
    """Advanced Arabic to French translator for personal information"""
    
    def __init__(self):
        """Initialize translation models and fallback dictionaries"""
        self.model = None
        self.tokenizer = None
        self.google_translator = None
        
        # Initialize transformer model if available
        if TRANSFORMERS_AVAILABLE:
            try:
                self._load_transformer_model()
            except Exception as e:
                logger.error(f"Failed to load transformer model: {e}")
        
        # Initialize Google Translator as backup
        if GOOGLETRANS_AVAILABLE:
            try:
                self.google_translator = GoogleTranslator()
                logger.info("Google Translator initialized as backup")
            except Exception as e:
                logger.error(f"Failed to initialize Google Translator: {e}")
        
        # Fallback dictionary for common names and places
        self.name_dictionary = {
            # Common Tunisian first names
            'محمد': 'Mohamed',
            'أحمد': 'Ahmed',
            'علي': 'Ali',
            'حسن': 'Hassan',
            'حسين': 'Hussein',
            'عبد الله': 'Abdallah',
            'عبد الرحمن': 'Abderrahman',
            'عبد العزيز': 'Abdelaziz',
            'فاطمة': 'Fatma',
            'عائشة': 'Aicha',
            'خديجة': 'Khadija',
            'زينب': 'Zeineb',
            'مريم': 'Maryam',
            'سارة': 'Sara',
            'ليلى': 'Leila',
            'نور': 'Nour',
            'أمينة': 'Amina',
            'سلمى': 'Salma',
            
            # Common last names
            'بن علي': 'Ben Ali',
            'التونسي': 'Ettounsi',
            'الصغير': 'Esseghir',
            'الكبير': 'Elkebir',
            'بن عمر': 'Ben Omar',
            'بن محمد': 'Ben Mohamed',
            'الهادي': 'Elhadi',
            'النوري': 'Enouri',
            'الزهراني': 'Ezahrani',
            'القادري': 'Elkadri'
        }
        
        self.place_dictionary = {
            # Tunisian cities and regions
            'تونس': 'Tunis',
            'صفاقس': 'Sfax',
            'سوسة': 'Sousse',
            'القيروان': 'Kairouan',
            'بنزرت': 'Bizerte',
            'قابس': 'Gabès',
            'أريانة': 'Ariana',
            'منوبة': 'Manouba',
            'بن عروس': 'Ben Arous',
            'نابل': 'Nabeul',
            'زغوان': 'Zaghouan',
            'باجة': 'Béja',
            'جندوبة': 'Jendouba',
            'الكاف': 'Le Kef',
            'سليانة': 'Siliana',
            'القصرين': 'Kasserine',
            'سيدي بوزيد': 'Sidi Bouzid',
            'قفصة': 'Gafsa',
            'توزر': 'Tozeur',
            'قبلي': 'Kebili',
            'تطاوين': 'Tataouine',
            'مدنين': 'Medenine',
            'المهدية': 'Mahdia',
            'المنستير': 'Monastir',
            'قرطاج': 'Carthage',
            'حمام الأنف': 'Hammam Lif',
            'رادس': 'Radès'
        }
        
        # General translation dictionary
        self.general_dictionary = {
            'تونسي': 'Tunisien',
            'تونسية': 'Tunisienne',
            'الجمهورية التونسية': 'République Tunisienne',
            'ذكر': 'Masculin',
            'أنثى': 'Féminin',
            'أعزب': 'Célibataire',
            'متزوج': 'Marié',
            'متزوجة': 'Mariée',
            'مطلق': 'Divorcé',
            'مطلقة': 'Divorcée',
            'أرمل': 'Veuf',
            'أرملة': 'Veuve'
        }
    
    def _load_transformer_model(self):
        """Load the transformer model for translation"""
        try:
            model_name = TRANSLATION_CONFIG['model_name']
            self.tokenizer = MarianTokenizer.from_pretrained(model_name)
            self.model = MarianMTModel.from_pretrained(model_name)
            logger.info(f"Loaded transformer model: {model_name}")
        except Exception as e:
            logger.warning(f"Failed to load primary model, trying backup: {e}")
            try:
                backup_model = TRANSLATION_CONFIG['backup_model']
                self.tokenizer = MarianTokenizer.from_pretrained(backup_model)
                self.model = MarianMTModel.from_pretrained(backup_model)
                logger.info(f"Loaded backup model: {backup_model}")
            except Exception as e2:
                logger.error(f"Failed to load backup model: {e2}")
                self.model = None
                self.tokenizer = None
    
    def _clean_arabic_text(self, text: str) -> str:
        """Clean and normalize Arabic text for translation"""
        if not text:
            return ""
        
        # Remove diacritics
        text = ''.join(c for c in unicodedata.normalize('NFD', text) 
                      if unicodedata.category(c) != 'Mn')
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        # Remove non-Arabic, non-Latin characters except spaces and common punctuation
        text = re.sub(r'[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF'
                     r'A-Za-z0-9\s\-\.\,\(\)]', '', text)
        
        return text
    
    def _translate_with_transformer(self, text: str) -> Optional[str]:
        """Translate using transformer model"""
        if not self.model or not self.tokenizer:
            return None
        
        try:
            # Tokenize input
            inputs = self.tokenizer(text, return_tensors="pt", padding=True, truncation=True, 
                                  max_length=TRANSLATION_CONFIG['max_length'])
            
            # Generate translation
            outputs = self.model.generate(
                **inputs,
                max_length=TRANSLATION_CONFIG['max_length'],
                temperature=TRANSLATION_CONFIG['temperature'],
                do_sample=TRANSLATION_CONFIG['do_sample'],
                pad_token_id=self.tokenizer.pad_token_id
            )
            
            # Decode output
            translation = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            return translation.strip()
            
        except Exception as e:
            logger.error(f"Transformer translation failed: {e}")
            return None
    
    def _translate_with_google(self, text: str) -> Optional[str]:
        """Translate using Google Translate"""
        if not self.google_translator:
            return None
        
        try:
            result = self.google_translator.translate(text, src='ar', dest='fr')
            return result.text.strip()
        except Exception as e:
            logger.error(f"Google translation failed: {e}")
            return None
    
    def _translate_with_dictionary(self, text: str, field_type: str = None) -> Optional[str]:
        """Translate using fallback dictionaries"""
        text = text.strip()
        
        # Check specific dictionaries based on field type
        if field_type in ['first_name', 'last_name']:
            if text in self.name_dictionary:
                return self.name_dictionary[text]
        elif field_type in ['birth_place', 'address']:
            if text in self.place_dictionary:
                return self.place_dictionary[text]
        
        # Check general dictionary
        if text in self.general_dictionary:
            return self.general_dictionary[text]
        
        # Try partial matches for compound names/places
        for arabic, french in {**self.name_dictionary, **self.place_dictionary, **self.general_dictionary}.items():
            if arabic in text:
                return text.replace(arabic, french)
        
        return None
    
    def _is_arabic_text(self, text: str) -> bool:
        """Check if text contains Arabic characters"""
        arabic_chars = sum(1 for c in text if '\u0600' <= c <= '\u06FF' or 
                          '\u0750' <= c <= '\u077F' or 
                          '\u08A0' <= c <= '\u08FF')
        return arabic_chars > 0
    
    def translate_field(self, text: str, field_type: str = None, confidence: float = 1.0) -> Dict:
        """
        Translate a single field with multiple fallback methods
        """
        if not text or not text.strip():
            return {
                'original': text,
                'translated': text,
                'translation_method': 'no_translation_needed',
                'translation_confidence': 1.0,
                'field_type': field_type
            }
        
        cleaned_text = self._clean_arabic_text(text)
        
        # If no Arabic characters, return as is
        if not self._is_arabic_text(cleaned_text):
            return {
                'original': text,
                'translated': text,
                'translation_method': 'no_translation_needed',
                'translation_confidence': 1.0,
                'field_type': field_type
            }
        
        translation_methods = []
        
        # Try dictionary translation first (most reliable for names/places)
        dict_translation = self._translate_with_dictionary(cleaned_text, field_type)
        if dict_translation:
            translation_methods.append(('dictionary', dict_translation, 0.95))
        
        # Try transformer model
        transformer_translation = self._translate_with_transformer(cleaned_text)
        if transformer_translation:
            translation_methods.append(('transformer', transformer_translation, 0.85))
        
        # Try Google Translate
        google_translation = self._translate_with_google(cleaned_text)
        if google_translation:
            translation_methods.append(('google', google_translation, 0.75))
        
        # Choose best translation
        if translation_methods:
            # Sort by confidence and choose the best
            translation_methods.sort(key=lambda x: x[2], reverse=True)
            method, translation, method_confidence = translation_methods[0]
            
            # Adjust confidence based on original OCR confidence
            final_confidence = min(confidence * method_confidence, 1.0)
            
            return {
                'original': text,
                'translated': translation,
                'translation_method': method,
                'translation_confidence': final_confidence,
                'field_type': field_type,
                'alternative_translations': [
                    {'method': m, 'translation': t, 'confidence': c} 
                    for m, t, c in translation_methods[1:]
                ]
            }
        else:
            # No translation available, return original
            logger.warning(f"No translation available for: {text}")
            return {
                'original': text,
                'translated': text,
                'translation_method': 'failed',
                'translation_confidence': 0.0,
                'field_type': field_type
            }
    
    def translate_id_card_data(self, parsed_data: Dict) -> Dict:
        """
        Translate all extracted ID card data
        """
        logger.info("Starting translation of ID card data")
        
        translated_data = {
            'original_data': {},
            'translated_data': {},
            'translation_metadata': {
                'translation_timestamp': None,
                'translation_methods_used': [],
                'overall_translation_confidence': 0.0
            }
        }
        
        # Fields that need translation
        translatable_fields = ['first_name', 'last_name', 'birth_place', 'nationality', 'address']
        
        total_confidence = 0.0
        translated_count = 0
        
        for field in translatable_fields:
            if field in parsed_data and parsed_data[field] is not None:
                field_data = parsed_data[field]
                if isinstance(field_data, dict) and 'value' in field_data:
                    original_value = field_data['value']
                    ocr_confidence = field_data.get('confidence', 1.0)
                    
                    # Translate the field
                    translation_result = self.translate_field(original_value, field, ocr_confidence)
                    
                    # Store results
                    translated_data['original_data'][field] = field_data
                    translated_data['translated_data'][field] = {
                        **field_data,
                        'translated_value': translation_result['translated'],
                        'translation_info': translation_result
                    }
                    
                    # Track translation methods
                    method = translation_result['translation_method']
                    if method not in translated_data['translation_metadata']['translation_methods_used']:
                        translated_data['translation_metadata']['translation_methods_used'].append(method)
                    
                    # Calculate overall confidence
                    total_confidence += translation_result['translation_confidence']
                    translated_count += 1
        
        # Copy non-translatable fields
        non_translatable_fields = ['card_number', 'birth_date', 'extraction_metadata']
        for field in non_translatable_fields:
            if field in parsed_data:
                translated_data['original_data'][field] = parsed_data[field]
                translated_data['translated_data'][field] = parsed_data[field]
        
        # Calculate overall translation confidence
        if translated_count > 0:
            translated_data['translation_metadata']['overall_translation_confidence'] = total_confidence / translated_count
        
        from datetime import datetime
        translated_data['translation_metadata']['translation_timestamp'] = datetime.now().isoformat()
        
        logger.info(f"Translation completed. Overall confidence: "
                   f"{translated_data['translation_metadata']['overall_translation_confidence']:.2f}")
        
        return translated_data
