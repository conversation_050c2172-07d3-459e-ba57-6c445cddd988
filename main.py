"""
Main script for Tunisian ID Card OCR and Translation System
Orchestrates the complete pipeline: OCR extraction, parsing, translation, and data saving
"""

import logging
import sys
from pathlib import Path
from typing import List, Dict
import traceback
from datetime import datetime

# Import our custom modules
from config import DATA_DIR, LOGGING_CONFIG
from ocr_extractor import OCRExtractor
from id_card_parser import TunisianIDCardParser
from arabic_translator import ArabicTranslator
from data_saver import DataSaver

# Configure logging
logging.basicConfig(
    level=getattr(logging, LOGGING_CONFIG['level']),
    format=LOGGING_CONFIG['format'],
    handlers=[
        logging.FileHandler(LOGGING_CONFIG['file'], encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class IDCardProcessor:
    """Main processor for Tunisian ID card extraction and translation"""
    
    def __init__(self):
        """Initialize all components"""
        logger.info("Initializing ID Card Processor")
        
        try:
            self.ocr_extractor = OCRExtractor()
            self.id_parser = TunisianIDCardParser()
            self.translator = ArabicTranslator()
            self.data_saver = DataSaver()
            
            logger.info("All components initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize components: {e}")
            raise
    
    def get_image_files(self, directory: Path) -> List[Path]:
        """Get all image files from the specified directory"""
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        
        image_files = []
        for ext in image_extensions:
            image_files.extend(directory.glob(f'*{ext}'))
            image_files.extend(directory.glob(f'*{ext.upper()}'))
        
        logger.info(f"Found {len(image_files)} image files in {directory}")
        return sorted(image_files)
    
    def process_single_card(self, image_path: Path) -> Dict:
        """Process a single ID card image"""
        logger.info(f"Processing ID card: {image_path.name}")
        
        try:
            # Step 1: OCR Extraction
            logger.info("Step 1: Extracting text with OCR")
            ocr_results = self.ocr_extractor.extract_text(str(image_path))
            
            if not ocr_results:
                raise ValueError("No text extracted from image")
            
            logger.info(f"OCR extracted {len(ocr_results)} text elements")
            
            # Step 2: Parse ID card information
            logger.info("Step 2: Parsing ID card information")
            parsed_data = self.id_parser.parse_id_card(ocr_results)
            
            # Step 3: Translate Arabic text to French
            logger.info("Step 3: Translating Arabic text to French")
            translated_data = self.translator.translate_id_card_data(parsed_data)
            
            # Add file information
            translated_data['file_info'] = {
                'file_path': str(image_path),
                'file_name': image_path.name,
                'processing_timestamp': datetime.now().isoformat()
            }
            
            # Calculate overall success metrics
            quality_score = parsed_data.get('extraction_metadata', {}).get('quality_score', 0)
            translation_confidence = translated_data.get('translation_metadata', {}).get('overall_translation_confidence', 0)
            
            logger.info(f"Processing completed for {image_path.name}")
            logger.info(f"Quality score: {quality_score:.2f}, Translation confidence: {translation_confidence:.2f}")
            
            return translated_data
            
        except Exception as e:
            logger.error(f"Failed to process {image_path.name}: {e}")
            logger.error(traceback.format_exc())
            
            return {
                'file_info': {
                    'file_path': str(image_path),
                    'file_name': image_path.name,
                    'processing_timestamp': datetime.now().isoformat()
                },
                'error': str(e),
                'error_traceback': traceback.format_exc()
            }
    
    def process_all_cards(self, data_directory: Path = None) -> List[Dict]:
        """Process all ID cards in the data directory"""
        if data_directory is None:
            data_directory = DATA_DIR
        
        logger.info(f"Starting batch processing of ID cards in: {data_directory}")
        
        # Get all image files
        image_files = self.get_image_files(data_directory)
        
        if not image_files:
            logger.warning(f"No image files found in {data_directory}")
            return []
        
        # Process each image
        results = []
        successful_count = 0
        failed_count = 0
        
        for i, image_path in enumerate(image_files, 1):
            logger.info(f"Processing file {i}/{len(image_files)}: {image_path.name}")
            
            result = self.process_single_card(image_path)
            results.append(result)
            
            if 'error' in result:
                failed_count += 1
            else:
                successful_count += 1
            
            # Log progress
            logger.info(f"Progress: {i}/{len(image_files)} files processed "
                       f"({successful_count} successful, {failed_count} failed)")
        
        logger.info(f"Batch processing completed: {successful_count} successful, {failed_count} failed")
        return results
    
    def save_results(self, results: List[Dict], base_filename: str = None) -> Dict:
        """Save processing results in multiple formats"""
        if base_filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_filename = f"tunisian_id_cards_{timestamp}"
        
        logger.info("Saving results in multiple formats")
        
        # Filter successful results for main data export
        successful_results = [r for r in results if 'error' not in r]
        
        # Save main data
        saved_files = {}
        if successful_results:
            saved_files = self.data_saver.save_all_formats(successful_results, base_filename)
        
        # Create and save processing report
        report = self.data_saver.create_processing_report(results)
        report_filename = f"{base_filename}_processing_report.json"
        report_path = self.data_saver.save_json(report, report_filename)
        saved_files['processing_report'] = report_path
        
        logger.info(f"Results saved in {len(saved_files)} files")
        return saved_files
    
    def run_complete_pipeline(self, data_directory: Path = None) -> Dict:
        """Run the complete ID card processing pipeline"""
        logger.info("=" * 60)
        logger.info("STARTING TUNISIAN ID CARD PROCESSING PIPELINE")
        logger.info("=" * 60)
        
        start_time = datetime.now()
        
        try:
            # Process all cards
            results = self.process_all_cards(data_directory)
            
            # Save results
            saved_files = self.save_results(results)
            
            # Calculate final statistics
            total_files = len(results)
            successful_files = len([r for r in results if 'error' not in r])
            failed_files = total_files - successful_files
            
            end_time = datetime.now()
            processing_time = end_time - start_time
            
            # Create final summary
            summary = {
                'pipeline_summary': {
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat(),
                    'processing_time_seconds': processing_time.total_seconds(),
                    'total_files': total_files,
                    'successful_files': successful_files,
                    'failed_files': failed_files,
                    'success_rate': (successful_files / total_files * 100) if total_files > 0 else 0
                },
                'saved_files': {str(k): str(v) for k, v in saved_files.items()},
                'results': results
            }
            
            logger.info("=" * 60)
            logger.info("PIPELINE COMPLETED SUCCESSFULLY")
            logger.info(f"Processed {total_files} files in {processing_time}")
            logger.info(f"Success rate: {summary['pipeline_summary']['success_rate']:.1f}%")
            logger.info("=" * 60)
            
            return summary
            
        except Exception as e:
            logger.error(f"Pipeline failed: {e}")
            logger.error(traceback.format_exc())
            raise

def main():
    """Main entry point"""
    try:
        # Initialize processor
        processor = IDCardProcessor()
        
        # Run the complete pipeline
        summary = processor.run_complete_pipeline()
        
        # Print summary to console
        print("\n" + "=" * 60)
        print("PROCESSING SUMMARY")
        print("=" * 60)
        print(f"Total files processed: {summary['pipeline_summary']['total_files']}")
        print(f"Successful extractions: {summary['pipeline_summary']['successful_files']}")
        print(f"Failed extractions: {summary['pipeline_summary']['failed_files']}")
        print(f"Success rate: {summary['pipeline_summary']['success_rate']:.1f}%")
        print(f"Processing time: {summary['pipeline_summary']['processing_time_seconds']:.1f} seconds")
        print("\nSaved files:")
        for file_type, file_path in summary['saved_files'].items():
            print(f"  {file_type}: {file_path}")
        print("=" * 60)
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("Processing interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Application failed: {e}")
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())
