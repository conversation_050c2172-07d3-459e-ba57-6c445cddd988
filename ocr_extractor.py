"""
OCR Extractor module for Tunisian ID Cards
Optimized for Arabic text extraction using Tesseract and EasyOCR
"""

import cv2
import numpy as np
import pytesseract
import easyocr
from PIL import Image, ImageEnhance, ImageFilter
import logging
from pathlib import Path
import uuid
from typing import List, Dict, Tuple, Optional
import re

from config import OCR_CONFIG, ANALYSIS_OUTPUT_DIR

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OCRExtractor:
    """Advanced OCR extractor for Tunisian ID cards with Arabic text support"""
    
    def __init__(self):
        """Initialize OCR engines"""
        try:
            # Initialize EasyOCR reader
            self.easyocr_reader = easyocr.Reader(
                OCR_CONFIG['easyocr']['languages'],
                gpu=OCR_CONFIG['easyocr']['gpu']
            )
            logger.info("EasyOCR initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize EasyOCR: {e}")
            self.easyocr_reader = None
        
        # Test Tesseract availability
        try:
            pytesseract.get_tesseract_version()
            logger.info("Tesseract initialized successfully")
        except Exception as e:
            logger.error(f"Tesseract not available: {e}")
    
    def preprocess_image(self, image_path: str, save_analysis: bool = True) -> np.ndarray:
        """
        Advanced image preprocessing for better OCR results
        """
        # Read image
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Could not load image: {image_path}")
        
        original = image.copy()
        
        # Generate unique ID for this processing session
        process_id = str(uuid.uuid4())
        
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        if save_analysis:
            cv2.imwrite(str(ANALYSIS_OUTPUT_DIR / f"{process_id}_gray.jpg"), gray)
        
        # Resize image for better OCR
        height, width = gray.shape
        resize_factor = OCR_CONFIG['preprocessing']['resize_factor']
        new_width = int(width * resize_factor)
        new_height = int(height * resize_factor)
        resized = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
        
        # Noise reduction
        denoised = cv2.bilateralFilter(
            resized,
            OCR_CONFIG['preprocessing']['bilateral_filter']['d'],
            OCR_CONFIG['preprocessing']['bilateral_filter']['sigmaColor'],
            OCR_CONFIG['preprocessing']['bilateral_filter']['sigmaSpace']
        )
        if save_analysis:
            cv2.imwrite(str(ANALYSIS_OUTPUT_DIR / f"{process_id}_denoised.jpg"), denoised)
        
        # Enhance contrast using CLAHE
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(denoised)
        if save_analysis:
            cv2.imwrite(str(ANALYSIS_OUTPUT_DIR / f"{process_id}_clahe.jpg"), enhanced)
        
        # Histogram equalization
        equalized = cv2.equalizeHist(enhanced)
        if save_analysis:
            cv2.imwrite(str(ANALYSIS_OUTPUT_DIR / f"{process_id}_equalized.jpg"), equalized)
        
        # Adaptive thresholding
        adaptive_thresh = cv2.adaptiveThreshold(
            equalized, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        if save_analysis:
            cv2.imwrite(str(ANALYSIS_OUTPUT_DIR / f"{process_id}_adaptive_thresh.jpg"), adaptive_thresh)
        
        # Otsu's thresholding
        _, otsu_thresh = cv2.threshold(equalized, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        if save_analysis:
            cv2.imwrite(str(ANALYSIS_OUTPUT_DIR / f"{process_id}_otsu.jpg"), otsu_thresh)
        
        # Morphological operations to clean up
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, OCR_CONFIG['preprocessing']['morphology_kernel_size'])
        morph = cv2.morphologyEx(otsu_thresh, cv2.MORPH_CLOSE, kernel)
        if save_analysis:
            cv2.imwrite(str(ANALYSIS_OUTPUT_DIR / f"{process_id}_morph.jpg"), morph)
        
        # Find and draw contours for analysis
        contours, _ = cv2.findContours(morph, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        contour_image = cv2.cvtColor(morph, cv2.COLOR_GRAY2BGR)
        cv2.drawContours(contour_image, contours, -1, (0, 255, 0), 2)
        if save_analysis:
            cv2.imwrite(str(ANALYSIS_OUTPUT_DIR / f"{process_id}_contours.jpg"), contour_image)
        
        # Final enhanced image
        final_enhanced = morph
        if save_analysis:
            cv2.imwrite(str(ANALYSIS_OUTPUT_DIR / f"{process_id}_enhanced.jpg"), final_enhanced)
        
        logger.info(f"Image preprocessing completed. Analysis saved with ID: {process_id}")
        return final_enhanced
    
    def extract_text_tesseract(self, image: np.ndarray) -> List[Dict]:
        """Extract text using Tesseract OCR"""
        try:
            # Get detailed data from Tesseract
            data = pytesseract.image_to_data(
                image,
                lang=OCR_CONFIG['tesseract']['lang'],
                config=OCR_CONFIG['tesseract']['config'],
                output_type=pytesseract.Output.DICT
            )
            
            results = []
            n_boxes = len(data['level'])
            
            for i in range(n_boxes):
                confidence = int(data['conf'][i])
                text = data['text'][i].strip()
                
                if confidence > 30 and len(text) > 1:  # Filter low confidence and empty text
                    x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                    
                    results.append({
                        'text': text,
                        'confidence': confidence / 100.0,  # Normalize to 0-1
                        'bbox': [x, y, x + w, y + h],
                        'engine': 'tesseract'
                    })
            
            logger.info(f"Tesseract extracted {len(results)} text elements")
            return results
            
        except Exception as e:
            logger.error(f"Tesseract OCR failed: {e}")
            return []
    
    def extract_text_easyocr(self, image: np.ndarray) -> List[Dict]:
        """Extract text using EasyOCR"""
        if self.easyocr_reader is None:
            logger.warning("EasyOCR not available")
            return []
        
        try:
            results = self.easyocr_reader.readtext(
                image,
                detail=OCR_CONFIG['easyocr']['detail'],
                paragraph=OCR_CONFIG['easyocr']['paragraph'],
                width_ths=OCR_CONFIG['easyocr']['width_ths'],
                height_ths=OCR_CONFIG['easyocr']['height_ths'],
                decoder=OCR_CONFIG['easyocr']['decoder']
            )
            
            formatted_results = []
            for result in results:
                bbox, text, confidence = result
                
                # Convert bbox to [x1, y1, x2, y2] format
                x_coords = [point[0] for point in bbox]
                y_coords = [point[1] for point in bbox]
                x1, y1, x2, y2 = min(x_coords), min(y_coords), max(x_coords), max(y_coords)
                
                formatted_results.append({
                    'text': text.strip(),
                    'confidence': confidence,
                    'bbox': [int(x1), int(y1), int(x2), int(y2)],
                    'engine': 'easyocr'
                })
            
            logger.info(f"EasyOCR extracted {len(formatted_results)} text elements")
            return formatted_results
            
        except Exception as e:
            logger.error(f"EasyOCR failed: {e}")
            return []
    
    def combine_ocr_results(self, tesseract_results: List[Dict], easyocr_results: List[Dict]) -> List[Dict]:
        """Combine and deduplicate results from both OCR engines"""
        all_results = tesseract_results + easyocr_results
        
        # Sort by confidence
        all_results.sort(key=lambda x: x['confidence'], reverse=True)
        
        # Remove duplicates based on text similarity and bbox overlap
        unique_results = []
        for result in all_results:
            is_duplicate = False
            for existing in unique_results:
                # Check text similarity
                text_similarity = self._calculate_text_similarity(result['text'], existing['text'])
                bbox_overlap = self._calculate_bbox_overlap(result['bbox'], existing['bbox'])
                
                if text_similarity > 0.8 or bbox_overlap > 0.5:
                    # Keep the one with higher confidence
                    if result['confidence'] > existing['confidence']:
                        unique_results.remove(existing)
                        unique_results.append(result)
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_results.append(result)
        
        logger.info(f"Combined OCR results: {len(unique_results)} unique text elements")
        return unique_results
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two text strings"""
        if not text1 or not text2:
            return 0.0
        
        # Simple character-based similarity
        set1, set2 = set(text1.lower()), set(text2.lower())
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0
    
    def _calculate_bbox_overlap(self, bbox1: List[int], bbox2: List[int]) -> float:
        """Calculate overlap ratio between two bounding boxes"""
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2
        
        # Calculate intersection
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
        
        intersection_area = (x2_i - x1_i) * (y2_i - y1_i)
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union_area = area1 + area2 - intersection_area
        
        return intersection_area / union_area if union_area > 0 else 0.0
    
    def extract_text(self, image_path: str) -> List[Dict]:
        """
        Main method to extract text from an ID card image
        """
        logger.info(f"Starting OCR extraction for: {image_path}")
        
        # Preprocess image
        processed_image = self.preprocess_image(image_path)
        
        # Extract text using both engines
        tesseract_results = self.extract_text_tesseract(processed_image)
        easyocr_results = self.extract_text_easyocr(processed_image)
        
        # Combine results
        combined_results = self.combine_ocr_results(tesseract_results, easyocr_results)
        
        logger.info(f"OCR extraction completed. Found {len(combined_results)} text elements")
        return combined_results
