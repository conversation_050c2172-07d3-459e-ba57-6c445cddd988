"""
Test script for the improved parser
"""

from ocr_extractor import OCRExtractor
from id_card_parser import TunisianIDCardParser
import json

def main():
    # Initialize components
    extractor = OCRExtractor()
    parser = TunisianIDCardParser()
    
    # Extract text from test image
    image_path = "data/test.jpg"
    print(f"Testing parser with: {image_path}")
    
    ocr_results = extractor.extract_text(image_path)
    print(f"\nOCR extracted {len(ocr_results)} text elements")
    
    # Parse the results
    parsed_data = parser.parse_id_card(ocr_results)
    
    print("\n" + "=" * 60)
    print("PARSING RESULTS")
    print("=" * 60)
    
    # Show extracted fields
    for field, data in parsed_data.items():
        if field != 'extraction_metadata':
            if data is not None:
                if isinstance(data, dict) and 'value' in data:
                    print(f"{field.upper()}: {data['value']} (confidence: {data['confidence']:.2f})")
                    print(f"  Source: '{data['source_text']}'")
                else:
                    print(f"{field.upper()}: {data}")
            else:
                print(f"{field.upper()}: Not found")
    
    print(f"\nQuality Score: {parsed_data['extraction_metadata']['quality_score']:.2f}")
    print(f"Raw OCR texts: {parsed_data['extraction_metadata']['raw_ocr_texts']}")
    
    # Save detailed results
    with open("test_parser_results.json", "w", encoding="utf-8") as f:
        json.dump(parsed_data, f, ensure_ascii=False, indent=2)
    
    print(f"\nDetailed results saved to: test_parser_results.json")

if __name__ == "__main__":
    main()
