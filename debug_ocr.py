"""
Debug script to see what OCR extracted from the test image
"""

from ocr_extractor import OCRExtractor
import json

def main():
    # Initialize OCR extractor
    extractor = OCRExtractor()
    
    # Extract text from test image
    image_path = "data/test.jpg"
    print(f"Extracting text from: {image_path}")
    
    ocr_results = extractor.extract_text(image_path)
    
    print(f"\nFound {len(ocr_results)} text elements:")
    print("=" * 60)
    
    for i, result in enumerate(ocr_results, 1):
        print(f"Element {i}:")
        print(f"  Text: '{result['text']}'")
        print(f"  Confidence: {result['confidence']:.2f}")
        print(f"  Engine: {result['engine']}")
        print(f"  BBox: {result['bbox']}")
        print("-" * 40)
    
    # Save detailed results
    with open("debug_ocr_results.json", "w", encoding="utf-8") as f:
        json.dump(ocr_results, f, ensure_ascii=False, indent=2)
    
    print(f"\nDetailed results saved to: debug_ocr_results.json")

if __name__ == "__main__":
    main()
