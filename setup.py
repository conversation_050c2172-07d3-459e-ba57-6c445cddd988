"""
Setup script for Tunisian ID Card OCR and Translation System
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def check_tesseract():
    """Check if Tesseract is installed"""
    try:
        result = subprocess.run(['tesseract', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ Tesseract is already installed")
            return True
    except FileNotFoundError:
        pass
    
    print("✗ Tesseract not found")
    print("Please install Tesseract OCR:")
    print("Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki")
    print("Linux: sudo apt-get install tesseract-ocr tesseract-ocr-ara")
    print("macOS: brew install tesseract tesseract-lang")
    return False

def install_python_packages():
    """Install required Python packages"""
    print("\nInstalling Python packages...")
    
    # Upgrade pip first
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "Upgrading pip"):
        return False
    
    # Install packages from requirements.txt
    if not run_command(f"{sys.executable} -m pip install -r requirements.txt", "Installing Python packages"):
        return False
    
    return True

def download_language_models():
    """Download required language models"""
    print("\nDownloading language models...")
    
    # Download Arabic language data for Tesseract
    print("Note: Make sure Arabic language data is installed for Tesseract")
    print("This is usually included with Tesseract installation")
    
    # Download transformer models (they will be downloaded automatically on first use)
    print("Transformer models will be downloaded automatically on first use")
    
    return True

def create_directories():
    """Create necessary directories"""
    directories = ['data', 'output', 'analysis_output', 'temp']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ Created directory: {directory}")
    
    return True

def test_installation():
    """Test the installation"""
    print("\nTesting installation...")
    
    try:
        # Test imports
        import pytesseract
        import easyocr
        import cv2
        import transformers
        import pandas
        
        print("✓ All required packages imported successfully")
        
        # Test Tesseract
        try:
            version = pytesseract.get_tesseract_version()
            print(f"✓ Tesseract version: {version}")
        except Exception as e:
            print(f"✗ Tesseract test failed: {e}")
            return False
        
        # Test EasyOCR initialization
        try:
            reader = easyocr.Reader(['ar', 'en'], gpu=False)
            print("✓ EasyOCR initialized successfully")
        except Exception as e:
            print(f"✗ EasyOCR test failed: {e}")
            return False
        
        return True
        
    except ImportError as e:
        print(f"✗ Import test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("=" * 60)
    print("TUNISIAN ID CARD OCR SYSTEM SETUP")
    print("=" * 60)
    
    success = True
    
    # Check Tesseract
    if not check_tesseract():
        success = False
    
    # Create directories
    if not create_directories():
        success = False
    
    # Install Python packages
    if not install_python_packages():
        success = False
    
    # Download language models
    if not download_language_models():
        success = False
    
    # Test installation
    if success and not test_installation():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("✓ SETUP COMPLETED SUCCESSFULLY!")
        print("\nYou can now run the system with:")
        print("python main.py")
        print("\nMake sure to place your ID card images in the 'data' folder")
    else:
        print("✗ SETUP FAILED!")
        print("Please resolve the issues above and run setup again")
    print("=" * 60)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
