"""
Demonstration script with simulated OCR data to show the complete system functionality
"""

from id_card_parser import TunisianIDCardParser
from arabic_translator import ArabicTranslator
from data_saver import DataSaver
import json

def create_simulated_ocr_data():
    """Create simulated OCR data that represents a typical Tunisian ID card"""
    return [
        {
            "text": "الجمهورية التونسية",
            "confidence": 0.95,
            "bbox": [100, 50, 400, 100],
            "engine": "easyocr"
        },
        {
            "text": "بطاقة التعريف الوطنية",
            "confidence": 0.92,
            "bbox": [100, 120, 450, 170],
            "engine": "easyocr"
        },
        {
            "text": "12345678",
            "confidence": 0.98,
            "bbox": [300, 200, 400, 230],
            "engine": "tesseract"
        },
        {
            "text": "الاسم: محمد",
            "confidence": 0.88,
            "bbox": [100, 250, 250, 280],
            "engine": "easyocr"
        },
        {
            "text": "اللقب: بن علي",
            "confidence": 0.85,
            "bbox": [100, 290, 280, 320],
            "engine": "easyocr"
        },
        {
            "text": "تاريخ الولادة: 15/03/1985",
            "confidence": 0.90,
            "bbox": [100, 330, 350, 360],
            "engine": "tesseract"
        },
        {
            "text": "مكان الولادة: تونس",
            "confidence": 0.87,
            "bbox": [100, 370, 300, 400],
            "engine": "easyocr"
        },
        {
            "text": "الجنسية: تونسي",
            "confidence": 0.93,
            "bbox": [100, 410, 250, 440],
            "engine": "easyocr"
        },
        {
            "text": "العنوان: شارع الحبيب بورقيبة، تونس",
            "confidence": 0.82,
            "bbox": [100, 450, 450, 480],
            "engine": "easyocr"
        }
    ]

def main():
    print("=" * 60)
    print("DÉMONSTRATION DU SYSTÈME D'EXTRACTION OCR")
    print("Cartes d'Identité Tunisiennes")
    print("=" * 60)
    
    # Initialize components
    parser = TunisianIDCardParser()
    translator = ArabicTranslator()
    data_saver = DataSaver()
    
    # Create simulated OCR data
    print("\n1. Données OCR simulées créées")
    ocr_results = create_simulated_ocr_data()
    
    print(f"   - {len(ocr_results)} éléments de texte détectés")
    print("   - Texte arabe et numéros identifiés")
    
    # Parse the data
    print("\n2. Analyse et extraction des champs")
    parsed_data = parser.parse_id_card(ocr_results)
    
    print("   Champs extraits:")
    for field, data in parsed_data.items():
        if field != 'extraction_metadata' and data is not None:
            if isinstance(data, dict) and 'value' in data:
                print(f"   - {field}: {data['value']} (confiance: {data['confidence']:.2f})")
    
    print(f"   - Score de qualité: {parsed_data['extraction_metadata']['quality_score']:.2f}")
    
    # Translate the data
    print("\n3. Traduction arabe → français")
    translated_data = translator.translate_id_card_data(parsed_data)
    
    print("   Traductions effectuées:")
    for field in ['first_name', 'last_name', 'birth_place', 'nationality', 'address']:
        if field in translated_data['translated_data'] and translated_data['translated_data'][field]:
            original = translated_data['original_data'][field]['value']
            translated = translated_data['translated_data'][field]['translated_value']
            method = translated_data['translated_data'][field]['translation_info']['translation_method']
            print(f"   - {field}: '{original}' → '{translated}' (méthode: {method})")
    
    confidence = translated_data['translation_metadata']['overall_translation_confidence']
    print(f"   - Confiance globale de traduction: {confidence:.2f}")
    
    # Save the results
    print("\n4. Sauvegarde des résultats")
    
    # Add file info for demo
    translated_data['file_info'] = {
        'file_path': 'demo/simulated_tunisian_id.jpg',
        'file_name': 'simulated_tunisian_id.jpg',
        'processing_timestamp': '2025-08-09T05:20:00.000000'
    }
    
    # Save in multiple formats
    saved_files = data_saver.save_all_formats([translated_data], "demo_tunisian_id_card")
    
    print("   Fichiers sauvegardés:")
    for file_type, file_path in saved_files.items():
        print(f"   - {file_type}: {file_path}")
    
    # Create a summary report
    print("\n5. Rapport de démonstration")
    
    summary = {
        "demo_summary": {
            "system_components": [
                "OCR multi-moteur (Tesseract + EasyOCR)",
                "Parser spécialisé pour cartes tunisiennes",
                "Traducteur arabe-français avec dictionnaires",
                "Exporteur multi-format (JSON, CSV, Excel)"
            ],
            "extracted_fields": list(parsed_data.keys()),
            "translation_methods": translated_data['translation_metadata']['translation_methods_used'],
            "quality_metrics": {
                "extraction_quality": parsed_data['extraction_metadata']['quality_score'],
                "translation_confidence": translated_data['translation_metadata']['overall_translation_confidence']
            }
        },
        "sample_results": {
            "original_arabic": {
                "name": "محمد",
                "surname": "بن علي",
                "birthplace": "تونس",
                "nationality": "تونسي"
            },
            "translated_french": {
                "name": "Mohamed",
                "surname": "Ben Ali", 
                "birthplace": "Tunis",
                "nationality": "Tunisien"
            }
        }
    }
    
    # Save demo report
    demo_report_path = data_saver.save_json(summary, "demo_system_report.json")
    
    print(f"   - Rapport de démonstration: {demo_report_path}")
    
    print("\n" + "=" * 60)
    print("DÉMONSTRATION TERMINÉE AVEC SUCCÈS")
    print("=" * 60)
    print("\nLe système est prêt à traiter de vraies cartes d'identité tunisiennes!")
    print("Placez vos images dans le dossier 'data/' et exécutez 'python main.py'")
    print("\nFonctionnalités démontrées:")
    print("✓ Extraction OCR optimisée pour l'arabe")
    print("✓ Parsing intelligent des champs de cartes tunisiennes")
    print("✓ Traduction automatique arabe → français")
    print("✓ Export multi-format avec rapports détaillés")
    print("✓ Gestion des erreurs et métriques de qualité")

if __name__ == "__main__":
    main()
