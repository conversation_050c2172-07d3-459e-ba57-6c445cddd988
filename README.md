# Système d'Extraction OCR pour Cartes d'Identité Tunisiennes

Ce système utilise des technologies OCR avancées et des modèles de traduction pour extraire et traduire automatiquement les informations des cartes d'identité tunisiennes de l'arabe vers le français.

## Fonctionnalités

- **OCR Multi-moteur** : Utilise Tesseract et EasyOCR pour une extraction optimale
- **Support de l'arabe** : Optimisé spécifiquement pour le texte arabe des cartes tunisiennes
- **Traduction automatique** : Traduit les informations personnelles de l'arabe vers le français
- **Préprocessing d'images** : Améliore automatiquement la qualité des images pour un meilleur OCR
- **Exports multiples** : Sauvegarde en JSON, CSV et Excel
- **Rapports détaillés** : Génère des rapports de qualité et de confiance

## Informations Extraites

- Numéro de carte d'identité
- Prénom (arabe → français)
- Nom de famille (arabe → français)
- Date de naissance
- Lieu de naissance (arabe → français)
- Nationalité (arabe → français)
- Adresse (arabe → français)

## Installation

### Prérequis

1. **Python 3.8+**
2. **Tesseract OCR** avec support arabe
   - Windows : [Télécharger ici](https://github.com/UB-Mannheim/tesseract/wiki)
   - Linux : `sudo apt-get install tesseract-ocr tesseract-ocr-ara`
   - macOS : `brew install tesseract tesseract-lang`

### Installation automatique

```bash
# Cloner ou télécharger le projet
cd carte

# Exécuter le script d'installation
python setup.py
```

### Installation manuelle

```bash
# Installer les dépendances Python
pip install -r requirements.txt

# Créer les dossiers nécessaires
mkdir -p data output analysis_output temp
```

## Utilisation

### 1. Préparation des images

Placez vos images de cartes d'identité tunisiennes dans le dossier `data/` :

```
data/
├── carte1.jpg
├── carte2.png
├── carte3.jpeg
└── ...
```

**Formats supportés** : JPG, JPEG, PNG, BMP, TIFF

### 2. Exécution du traitement

```bash
# Traiter toutes les cartes dans le dossier data
python main.py
```

### 3. Résultats

Les résultats seront sauvegardés dans le dossier `output/` :

```
output/
├── json/           # Fichiers JSON individuels et combinés
├── csv/            # Fichier CSV avec toutes les données
├── excel/          # Fichier Excel avec feuilles multiples
└── processing_report.json  # Rapport de traitement
```

## Structure des Résultats

### Données extraites (exemple JSON)

```json
{
  "original_data": {
    "card_number": {
      "value": "12345678",
      "confidence": 0.95,
      "bbox": [100, 50, 200, 80]
    },
    "first_name": {
      "value": "محمد",
      "confidence": 0.88
    }
  },
  "translated_data": {
    "first_name": {
      "value": "محمد",
      "translated_value": "Mohamed",
      "confidence": 0.88,
      "translation_info": {
        "translation_method": "dictionary",
        "translation_confidence": 0.95
      }
    }
  },
  "translation_metadata": {
    "overall_translation_confidence": 0.87,
    "translation_methods_used": ["dictionary", "transformer"]
  }
}
```

### Fichier Excel

Le fichier Excel contient 3 feuilles :
- **ID_Cards_Data** : Données complètes avec tous les détails
- **Summary** : Résumé avec original et traduction côte à côte
- **Statistics** : Statistiques de réussite et qualité

## Configuration

Modifiez `config.py` pour ajuster :

- **Paramètres OCR** : Seuils de confiance, langues
- **Modèles de traduction** : Choix des modèles Hugging Face
- **Formats de sortie** : JSON, CSV, Excel
- **Qualité** : Seuils de qualité minimale

## Modules du Système

### 1. `ocr_extractor.py`
- Préprocessing avancé des images
- Extraction OCR avec Tesseract et EasyOCR
- Combinaison et déduplication des résultats

### 2. `id_card_parser.py`
- Parsing spécialisé pour cartes tunisiennes
- Reconnaissance des patterns arabes et français
- Validation des données extraites

### 3. `arabic_translator.py`
- Traduction arabe → français
- Dictionnaires spécialisés pour noms tunisiens
- Modèles transformer de secours

### 4. `data_saver.py`
- Export multi-format
- Génération de rapports
- Statistiques de qualité

### 5. `main.py`
- Orchestration complète
- Traitement par lots
- Gestion d'erreurs

## Optimisation de la Qualité

### Conseils pour de meilleurs résultats

1. **Qualité des images** :
   - Résolution minimale : 300 DPI
   - Bon éclairage, sans reflets
   - Image droite (non inclinée)

2. **Format des cartes** :
   - Cartes complètes et lisibles
   - Texte non masqué ou endommagé

3. **Configuration** :
   - Ajustez les seuils dans `config.py`
   - Testez différents paramètres de préprocessing

## Dépannage

### Erreurs communes

1. **"Tesseract not found"**
   ```bash
   # Vérifier l'installation
   tesseract --version
   
   # Ajouter au PATH si nécessaire (Windows)
   set PATH=%PATH%;C:\Program Files\Tesseract-OCR
   ```

2. **"No text extracted"**
   - Vérifiez la qualité de l'image
   - Essayez avec une résolution plus élevée
   - Vérifiez que le texte est lisible

3. **"Translation failed"**
   - Vérifiez la connexion internet (pour Google Translate)
   - Les modèles transformer se téléchargent automatiquement

### Logs et débogage

Les logs détaillés sont sauvegardés dans `id_card_extraction.log`

Les images de débogage sont dans `analysis_output/` pour analyser le préprocessing

## Performance

- **Temps de traitement** : ~10-30 secondes par carte
- **Précision OCR** : 85-95% selon la qualité de l'image
- **Précision traduction** : 90-98% pour les noms courants

## Licence

Ce projet est développé pour l'extraction de données des cartes d'identité tunisiennes.

## Support

Pour des questions ou améliorations, consultez les logs d'erreur et ajustez la configuration selon vos besoins spécifiques.
