"""
Test script for the enhanced system with simulated data
Tests the improved regex patterns and intelligent parsing
"""

from intelligent_parser import IntelligentIDCardParser
from enhanced_translator import EnhancedArabicTranslator
import json

def create_enhanced_test_data():
    """Create enhanced test OCR data with more realistic Tunisian ID card content"""
    return [
        {
            "text": "الجمهورية التونسية",
            "confidence": 0.95,
            "bbox": [100, 50, 400, 100],
            "engine": "easyocr"
        },
        {
            "text": "بطاقة التعريف الوطنية",
            "confidence": 0.92,
            "bbox": [100, 120, 450, 170],
            "engine": "easyocr"
        },
        {
            "text": "رقم البطاقة: 12345678",
            "confidence": 0.98,
            "bbox": [100, 200, 350, 230],
            "engine": "tesseract"
        },
        {
            "text": "الاسم: محمد علي",
            "confidence": 0.88,
            "bbox": [100, 250, 280, 280],
            "engine": "easyocr"
        },
        {
            "text": "اللقب: بن سالم",
            "confidence": 0.85,
            "bbox": [100, 290, 280, 320],
            "engine": "easyocr"
        },
        {
            "text": "تاريخ الولادة: 25/12/1990",
            "confidence": 0.90,
            "bbox": [100, 330, 350, 360],
            "engine": "tesseract"
        },
        {
            "text": "مكان الولادة: صفاقس",
            "confidence": 0.87,
            "bbox": [100, 370, 300, 400],
            "engine": "easyocr"
        },
        {
            "text": "الجنسية: تونسي",
            "confidence": 0.93,
            "bbox": [100, 410, 250, 440],
            "engine": "easyocr"
        },
        {
            "text": "العنوان: شارع الحبيب بورقيبة، تونس",
            "confidence": 0.82,
            "bbox": [100, 450, 450, 480],
            "engine": "easyocr"
        },
        # Additional fragmented text (realistic OCR errors)
        {
            "text": "محمد",
            "confidence": 0.75,
            "bbox": [150, 250, 200, 280],
            "engine": "tesseract"
        },
        {
            "text": "بن",
            "confidence": 0.70,
            "bbox": [150, 290, 180, 320],
            "engine": "tesseract"
        },
        {
            "text": "25/12/1990",
            "confidence": 0.95,
            "bbox": [250, 330, 350, 360],
            "engine": "tesseract"
        }
    ]

def test_intelligent_parser():
    """Test the intelligent parser with enhanced regex patterns"""
    print("=" * 60)
    print("TESTING INTELLIGENT PARSER WITH ENHANCED REGEX")
    print("=" * 60)
    
    # Initialize parser
    parser = IntelligentIDCardParser()
    
    # Create test data
    ocr_results = create_enhanced_test_data()
    print(f"Created {len(ocr_results)} OCR test elements")
    
    # Parse the data
    print("\nParsing with intelligent algorithms...")
    parsed_data = parser.parse_id_card(ocr_results)
    
    print("\nINTELLIGENT PARSING RESULTS:")
    print("-" * 40)
    
    for field, data in parsed_data.items():
        if field != 'extraction_metadata' and data is not None:
            if isinstance(data, dict) and 'value' in data:
                confidence = data.get('final_score', data.get('confidence', 0))
                validation_score = data.get('validation_score', 0)
                pattern_match = data.get('pattern_match', False)
                ner_match = data.get('ner_match', False)
                
                print(f"{field.upper()}:")
                print(f"  Value: '{data['value']}'")
                print(f"  Confidence: {confidence:.2f}")
                print(f"  Validation Score: {validation_score:.2f}")
                print(f"  Pattern Match: {pattern_match}")
                print(f"  NER Match: {ner_match}")
                print(f"  Source: '{data['source_text']}'")
                print()
    
    quality_score = parsed_data['extraction_metadata']['quality_score']
    extraction_methods = parsed_data['extraction_metadata']['extraction_methods']
    
    print(f"OVERALL QUALITY SCORE: {quality_score:.2f}")
    print(f"EXTRACTION METHODS USED: {extraction_methods}")
    
    return parsed_data

def test_enhanced_translator():
    """Test the enhanced translator with multiple models"""
    print("\n" + "=" * 60)
    print("TESTING ENHANCED TRANSLATOR WITH MULTIPLE AI MODELS")
    print("=" * 60)
    
    # Initialize translator
    translator = EnhancedArabicTranslator()
    
    # Test cases with various Arabic texts
    test_cases = [
        ("محمد علي", "first_name"),
        ("بن سالم", "last_name"),
        ("صفاقس", "birth_place"),
        ("تونسي", "nationality"),
        ("شارع الحبيب بورقيبة", "address"),
        ("الجمهورية التونسية", "nationality"),
        ("بطاقة التعريف", "document_type")
    ]
    
    print("ENHANCED TRANSLATION RESULTS:")
    print("-" * 40)
    
    for arabic_text, field_type in test_cases:
        result = translator.translate_field(arabic_text, field_type, 0.9)
        
        print(f"Field: {field_type}")
        print(f"  Original: '{result['original']}'")
        print(f"  Translated: '{result['translated']}'")
        print(f"  Method: {result['translation_method']}")
        print(f"  Confidence: {result['translation_confidence']:.2f}")
        
        if 'alternative_translations' in result:
            print("  Alternatives:")
            for alt in result['alternative_translations']:
                print(f"    {alt['method']}: '{alt['translation']}' ({alt['confidence']:.2f})")
        print()
    
    return True

def test_complete_enhanced_pipeline():
    """Test the complete enhanced pipeline"""
    print("\n" + "=" * 60)
    print("TESTING COMPLETE ENHANCED PIPELINE")
    print("=" * 60)
    
    # Test parsing
    parsed_data = test_intelligent_parser()
    
    # Test translation
    translator = EnhancedArabicTranslator()
    translated_data = translator.translate_id_card_data(parsed_data)
    
    print("\nCOMPLETE PIPELINE RESULTS:")
    print("-" * 40)
    
    # Show final results
    for field in ['first_name', 'last_name', 'birth_place', 'nationality', 'address']:
        if field in translated_data['translated_data'] and translated_data['translated_data'][field]:
            original = translated_data['original_data'][field]['value']
            translated = translated_data['translated_data'][field]['translated_value']
            method = translated_data['translated_data'][field]['translation_info']['translation_method']
            confidence = translated_data['translated_data'][field]['translation_info']['translation_confidence']
            
            print(f"{field.upper()}:")
            print(f"  Arabic: '{original}' → French: '{translated}'")
            print(f"  Method: {method}, Confidence: {confidence:.2f}")
            print()
    
    # Overall metrics
    overall_translation_confidence = translated_data['translation_metadata']['overall_translation_confidence']
    methods_used = translated_data['translation_metadata']['translation_methods_used']
    
    print(f"OVERALL TRANSLATION CONFIDENCE: {overall_translation_confidence:.2f}")
    print(f"TRANSLATION METHODS USED: {methods_used}")
    
    # Save test results
    with open("enhanced_test_results.json", "w", encoding="utf-8") as f:
        json.dump(translated_data, f, ensure_ascii=False, indent=2)
    
    print(f"\nDetailed results saved to: enhanced_test_results.json")
    
    return translated_data

def main():
    """Main test function"""
    print("🚀 TESTING ENHANCED TUNISIAN ID CARD PROCESSING SYSTEM")
    print("Using Advanced Regex Patterns and AI Models")
    print("=" * 80)
    
    try:
        # Test individual components
        test_intelligent_parser()
        test_enhanced_translator()
        
        # Test complete pipeline
        result = test_complete_enhanced_pipeline()
        
        print("\n" + "=" * 80)
        print("✅ ALL ENHANCED TESTS COMPLETED SUCCESSFULLY!")
        print("=" * 80)
        
        # Summary
        print("\nENHANCEMENTS DEMONSTRATED:")
        print("✓ Advanced regex patterns with multiple variations")
        print("✓ Intelligent field validation and scoring")
        print("✓ Enhanced Arabic text normalization")
        print("✓ Multiple translation models with fallbacks")
        print("✓ Fuzzy matching for name/place recognition")
        print("✓ Confidence scoring and method tracking")
        print("✓ Comprehensive error handling")
        
        return True
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
