# Enhanced OCR libraries
pytesseract>=0.3.10
easyocr>=1.7.0
opencv-python>=4.8.0
Pillow>=10.0.0

# Advanced OCR engines
paddlepaddle>=2.5.0
paddleocr>=2.7.0

# Enhanced Machine Learning and NLP
transformers>=4.35.0
torch>=2.1.0
torchvision>=0.16.0
numpy>=1.24.0
sentencepiece>=0.1.99

# Advanced translation models
sacremoses>=0.0.53

# Named Entity Recognition
datasets>=2.14.0

# Translation libraries
googletrans==4.0.0rc1

# Data processing and export
pandas>=2.0.0
openpyxl>=3.1.0

# Text processing and similarity
unicodedata2>=15.0.0
python-bidi>=0.4.2
difflib2>=0.1.0

# Utilities
pathlib2>=2.3.7
uuid>=1.30
logging>=0.4.9.6

# Image processing enhancements
scikit-image>=0.21.0
imageio>=2.31.0

# Performance optimization
numba>=0.58.0

# Optional: GPU support for PyTorch (uncomment if you have CUDA)
# torch-audio>=2.1.0
# torchvision>=0.16.0

# Optional: Additional OCR engines (uncomment to install)
# surya-ocr>=0.4.0
# doctr>=0.7.0

# Optional: Advanced NLP models (uncomment for better NER)
# spacy>=3.7.0
# ar-core-news-sm @ https://github.com/explosion/spacy-models/releases/download/ar_core_news_sm-3.7.0/ar_core_news_sm-3.7.0-py3-none-any.whl
