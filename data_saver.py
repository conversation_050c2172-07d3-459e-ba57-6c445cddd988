"""
Data Saver module for ID Card extraction results
Saves extracted and translated data in multiple formats (JSON, CSV, Excel)
"""

import json
import csv
import logging
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime
import uuid

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    logging.warning("Pandas not available. Excel export will be disabled.")

try:
    import openpyxl
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    logging.warning("Openpyxl not available. Excel export may be limited.")

from config import OUTPUT_CONFIG, OUTPUT_DIR

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataSaver:
    """Advanced data saver for ID card extraction results"""
    
    def __init__(self):
        """Initialize the data saver"""
        self.output_dir = OUTPUT_DIR
        self.config = OUTPUT_CONFIG
        
        # Ensure output directory exists
        self.output_dir.mkdir(exist_ok=True)
        
        # Create subdirectories for different formats
        self.json_dir = self.output_dir / "json"
        self.csv_dir = self.output_dir / "csv"
        self.excel_dir = self.output_dir / "excel"
        
        for directory in [self.json_dir, self.csv_dir, self.excel_dir]:
            directory.mkdir(exist_ok=True)
    
    def _generate_filename(self, base_name: str, extension: str, timestamp: bool = True) -> str:
        """Generate a unique filename"""
        if timestamp:
            timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S")
            return f"{base_name}_{timestamp_str}.{extension}"
        else:
            return f"{base_name}.{extension}"
    
    def _flatten_data_for_csv(self, data: Dict) -> Dict:
        """Flatten nested data structure for CSV export"""
        flattened = {}
        
        def flatten_dict(d: Dict, prefix: str = ""):
            for key, value in d.items():
                new_key = f"{prefix}_{key}" if prefix else key
                
                if isinstance(value, dict):
                    if 'value' in value:
                        # This is a field with metadata
                        flattened[new_key] = value['value']
                        if 'translated_value' in value:
                            flattened[f"{new_key}_translated"] = value['translated_value']
                        if 'confidence' in value:
                            flattened[f"{new_key}_confidence"] = value['confidence']
                        if 'translation_info' in value and 'translation_confidence' in value['translation_info']:
                            flattened[f"{new_key}_translation_confidence"] = value['translation_info']['translation_confidence']
                    else:
                        # Nested dictionary
                        flatten_dict(value, new_key)
                elif isinstance(value, list):
                    # Convert list to string representation
                    flattened[new_key] = str(value)
                else:
                    flattened[new_key] = value
        
        flatten_dict(data)
        return flattened
    
    def save_json(self, data: Dict, filename: str = None) -> Path:
        """Save data as JSON file"""
        if filename is None:
            filename = self._generate_filename("id_card_data", "json")
        
        filepath = self.json_dir / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=self.config['json_indent'])
            
            logger.info(f"JSON data saved to: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Failed to save JSON data: {e}")
            raise
    
    def save_csv(self, data: List[Dict], filename: str = None) -> Path:
        """Save data as CSV file"""
        if filename is None:
            filename = self._generate_filename("id_card_data", "csv")
        
        filepath = self.csv_dir / filename
        
        if not data:
            logger.warning("No data to save to CSV")
            return filepath
        
        try:
            # Flatten all records
            flattened_data = [self._flatten_data_for_csv(record) for record in data]
            
            # Get all possible fieldnames
            fieldnames = set()
            for record in flattened_data:
                fieldnames.update(record.keys())
            fieldnames = sorted(list(fieldnames))
            
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames, delimiter=self.config['csv_delimiter'])
                writer.writeheader()
                writer.writerows(flattened_data)
            
            logger.info(f"CSV data saved to: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Failed to save CSV data: {e}")
            raise
    
    def save_excel(self, data: List[Dict], filename: str = None) -> Path:
        """Save data as Excel file"""
        if not PANDAS_AVAILABLE:
            logger.error("Pandas not available. Cannot save Excel file.")
            raise ImportError("Pandas is required for Excel export")
        
        if filename is None:
            filename = self._generate_filename("id_card_data", "xlsx")
        
        filepath = self.excel_dir / filename
        
        if not data:
            logger.warning("No data to save to Excel")
            return filepath
        
        try:
            # Flatten all records
            flattened_data = [self._flatten_data_for_csv(record) for record in data]
            
            # Create DataFrame
            df = pd.DataFrame(flattened_data)
            
            # Create Excel writer with multiple sheets
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # Main data sheet
                df.to_excel(writer, sheet_name=self.config['excel_sheet_name'], index=False)
                
                # Summary sheet
                summary_data = self._create_summary_data(data)
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)
                
                # Statistics sheet
                stats_data = self._create_statistics_data(data)
                stats_df = pd.DataFrame(stats_data, index=[0])
                stats_df.to_excel(writer, sheet_name='Statistics', index=False)
            
            logger.info(f"Excel data saved to: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Failed to save Excel data: {e}")
            raise
    
    def _create_summary_data(self, data: List[Dict]) -> List[Dict]:
        """Create summary data for Excel export"""
        summary = []
        
        for i, record in enumerate(data, 1):
            translated_data = record.get('translated_data', {})
            
            summary_record = {
                'Record_Number': i,
                'Card_Number': self._get_field_value(translated_data, 'card_number'),
                'First_Name_Original': self._get_field_value(translated_data, 'first_name'),
                'First_Name_Translated': self._get_translated_value(translated_data, 'first_name'),
                'Last_Name_Original': self._get_field_value(translated_data, 'last_name'),
                'Last_Name_Translated': self._get_translated_value(translated_data, 'last_name'),
                'Birth_Date': self._get_field_value(translated_data, 'birth_date'),
                'Birth_Place_Original': self._get_field_value(translated_data, 'birth_place'),
                'Birth_Place_Translated': self._get_translated_value(translated_data, 'birth_place'),
                'Overall_Quality': record.get('translated_data', {}).get('extraction_metadata', {}).get('quality_score', 0),
                'Translation_Confidence': record.get('translation_metadata', {}).get('overall_translation_confidence', 0)
            }
            
            summary.append(summary_record)
        
        return summary
    
    def _create_statistics_data(self, data: List[Dict]) -> Dict:
        """Create statistics data for Excel export"""
        if not data:
            return {}
        
        total_records = len(data)
        
        # Count successful extractions
        successful_extractions = {
            'card_number': 0,
            'first_name': 0,
            'last_name': 0,
            'birth_date': 0,
            'birth_place': 0
        }
        
        # Count successful translations
        successful_translations = {
            'first_name': 0,
            'last_name': 0,
            'birth_place': 0
        }
        
        total_quality = 0
        total_translation_confidence = 0
        
        for record in data:
            translated_data = record.get('translated_data', {})
            
            # Count extractions
            for field in successful_extractions:
                if self._get_field_value(translated_data, field):
                    successful_extractions[field] += 1
            
            # Count translations
            for field in successful_translations:
                if self._get_translated_value(translated_data, field):
                    successful_translations[field] += 1
            
            # Sum quality scores
            quality = translated_data.get('extraction_metadata', {}).get('quality_score', 0)
            total_quality += quality
            
            translation_conf = record.get('translation_metadata', {}).get('overall_translation_confidence', 0)
            total_translation_confidence += translation_conf
        
        stats = {
            'Total_Records': total_records,
            'Average_Quality_Score': total_quality / total_records if total_records > 0 else 0,
            'Average_Translation_Confidence': total_translation_confidence / total_records if total_records > 0 else 0,
            'Card_Number_Success_Rate': successful_extractions['card_number'] / total_records * 100,
            'First_Name_Extraction_Rate': successful_extractions['first_name'] / total_records * 100,
            'Last_Name_Extraction_Rate': successful_extractions['last_name'] / total_records * 100,
            'Birth_Date_Extraction_Rate': successful_extractions['birth_date'] / total_records * 100,
            'Birth_Place_Extraction_Rate': successful_extractions['birth_place'] / total_records * 100,
            'First_Name_Translation_Rate': successful_translations['first_name'] / total_records * 100,
            'Last_Name_Translation_Rate': successful_translations['last_name'] / total_records * 100,
            'Birth_Place_Translation_Rate': successful_translations['birth_place'] / total_records * 100
        }
        
        return stats
    
    def _get_field_value(self, data: Dict, field: str) -> str:
        """Get the original value of a field"""
        field_data = data.get(field)
        if isinstance(field_data, dict) and 'value' in field_data:
            return field_data['value']
        return ""
    
    def _get_translated_value(self, data: Dict, field: str) -> str:
        """Get the translated value of a field"""
        field_data = data.get(field)
        if isinstance(field_data, dict) and 'translated_value' in field_data:
            return field_data['translated_value']
        return ""
    
    def save_all_formats(self, data: List[Dict], base_filename: str = None) -> Dict[str, Path]:
        """Save data in all configured formats"""
        if base_filename is None:
            base_filename = "id_card_extraction_results"
        
        saved_files = {}
        
        # Save individual records as JSON
        for i, record in enumerate(data):
            json_filename = f"{base_filename}_record_{i+1}.json"
            saved_files[f'json_record_{i+1}'] = self.save_json(record, json_filename)
        
        # Save combined data as JSON
        combined_json_filename = f"{base_filename}_combined.json"
        saved_files['json_combined'] = self.save_json(data, combined_json_filename)
        
        # Save as CSV
        if 'csv' in self.config['formats']:
            csv_filename = f"{base_filename}.csv"
            saved_files['csv'] = self.save_csv(data, csv_filename)
        
        # Save as Excel
        if 'excel' in self.config['formats'] and PANDAS_AVAILABLE:
            excel_filename = f"{base_filename}.xlsx"
            saved_files['excel'] = self.save_excel(data, excel_filename)
        
        logger.info(f"Data saved in {len(saved_files)} files")
        return saved_files
    
    def create_processing_report(self, processing_results: List[Dict]) -> Dict:
        """Create a comprehensive processing report"""
        report = {
            'processing_summary': {
                'total_files_processed': len(processing_results),
                'successful_extractions': 0,
                'failed_extractions': 0,
                'average_quality_score': 0,
                'average_translation_confidence': 0,
                'processing_timestamp': datetime.now().isoformat()
            },
            'file_details': [],
            'error_summary': []
        }
        
        total_quality = 0
        total_translation_confidence = 0
        
        for result in processing_results:
            if 'error' in result:
                report['processing_summary']['failed_extractions'] += 1
                report['error_summary'].append({
                    'file': result.get('file_path', 'unknown'),
                    'error': result['error']
                })
            else:
                report['processing_summary']['successful_extractions'] += 1
                
                # Extract quality metrics
                quality = result.get('translated_data', {}).get('extraction_metadata', {}).get('quality_score', 0)
                translation_conf = result.get('translation_metadata', {}).get('overall_translation_confidence', 0)
                
                total_quality += quality
                total_translation_confidence += translation_conf
                
                report['file_details'].append({
                    'file': result.get('file_path', 'unknown'),
                    'quality_score': quality,
                    'translation_confidence': translation_conf,
                    'extracted_fields': list(result.get('translated_data', {}).keys())
                })
        
        # Calculate averages
        successful_count = report['processing_summary']['successful_extractions']
        if successful_count > 0:
            report['processing_summary']['average_quality_score'] = total_quality / successful_count
            report['processing_summary']['average_translation_confidence'] = total_translation_confidence / successful_count
        
        return report
