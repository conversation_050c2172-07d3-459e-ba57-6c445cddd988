"""
Enhanced Main script with intelligent OCR, advanced parsing, and AI translation
Uses state-of-the-art models for perfect results
"""

import logging
import sys
from pathlib import Path
from typing import List, Dict
import traceback
from datetime import datetime

# Import enhanced modules
from config import DATA_DIR, LOGGING_CONFIG
from advanced_ocr_extractor import AdvancedOCRExtractor
from intelligent_parser import IntelligentIDCardParser
from enhanced_translator import EnhancedArabicTranslator
from data_saver import DataSaver

# Configure logging
logging.basicConfig(
    level=getattr(logging, LOGGING_CONFIG['level']),
    format=LOGGING_CONFIG['format'],
    handlers=[
        logging.FileHandler('enhanced_id_card_extraction.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class EnhancedIDCardProcessor:
    """Enhanced processor with state-of-the-art AI models"""
    
    def __init__(self):
        """Initialize all enhanced components"""
        logger.info("Initializing Enhanced ID Card Processor with AI models")
        
        try:
            self.ocr_extractor = AdvancedOCRExtractor()
            self.id_parser = IntelligentIDCardParser()
            self.translator = EnhancedArabicTranslator()
            self.data_saver = DataSaver()
            
            logger.info("All enhanced components initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize enhanced components: {e}")
            raise
    
    def get_image_files(self, directory: Path) -> List[Path]:
        """Get all image files from the specified directory"""
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        
        image_files = []
        for ext in image_extensions:
            image_files.extend(directory.glob(f'*{ext}'))
            image_files.extend(directory.glob(f'*{ext.upper()}'))
        
        # Remove duplicates and sort
        image_files = list(set(image_files))
        image_files.sort()
        
        logger.info(f"Found {len(image_files)} unique image files in {directory}")
        return image_files
    
    def process_single_card(self, image_path: Path) -> Dict:
        """Process a single ID card with enhanced AI pipeline"""
        logger.info(f"Processing ID card with enhanced AI: {image_path.name}")
        
        try:
            # Step 1: Advanced OCR Extraction with multiple engines
            logger.info("Step 1: Advanced OCR extraction with multiple AI models")
            ocr_results = self.ocr_extractor.extract_text(str(image_path))
            
            if not ocr_results:
                raise ValueError("No text extracted from image with advanced OCR")
            
            logger.info(f"Advanced OCR extracted {len(ocr_results)} high-quality text elements")
            
            # Log OCR results for debugging
            logger.info("OCR Results:")
            for i, result in enumerate(ocr_results[:10], 1):  # Log first 10 results
                logger.info(f"  {i}. '{result['text']}' (confidence: {result['confidence']:.2f}, engine: {result['engine']})")
            
            # Step 2: Intelligent parsing with advanced regex and NER
            logger.info("Step 2: Intelligent parsing with advanced regex and AI validation")
            parsed_data = self.id_parser.parse_id_card(ocr_results)
            
            # Log parsing results
            logger.info("Parsing Results:")
            for field, data in parsed_data.items():
                if field != 'extraction_metadata' and data is not None:
                    if isinstance(data, dict) and 'value' in data:
                        logger.info(f"  {field}: '{data['value']}' (confidence: {data.get('confidence', 0):.2f})")
            
            # Step 3: Enhanced translation with multiple AI models
            logger.info("Step 3: Enhanced AI translation with multiple models")
            translated_data = self.translator.translate_id_card_data(parsed_data)
            
            # Log translation results
            logger.info("Translation Results:")
            for field in ['first_name', 'last_name', 'birth_place', 'nationality', 'address']:
                if field in translated_data['translated_data'] and translated_data['translated_data'][field]:
                    original = translated_data['original_data'][field]['value']
                    translated = translated_data['translated_data'][field]['translated_value']
                    method = translated_data['translated_data'][field]['translation_info']['translation_method']
                    confidence = translated_data['translated_data'][field]['translation_info']['translation_confidence']
                    logger.info(f"  {field}: '{original}' → '{translated}' (method: {method}, confidence: {confidence:.2f})")
            
            # Add enhanced file information
            translated_data['file_info'] = {
                'file_path': str(image_path),
                'file_name': image_path.name,
                'processing_timestamp': datetime.now().isoformat(),
                'processing_method': 'enhanced_ai_pipeline'
            }
            
            # Calculate enhanced success metrics
            quality_score = parsed_data.get('extraction_metadata', {}).get('quality_score', 0)
            translation_confidence = translated_data.get('translation_metadata', {}).get('overall_translation_confidence', 0)
            
            # Enhanced overall score calculation
            ocr_quality = sum(r['confidence'] for r in ocr_results) / len(ocr_results) if ocr_results else 0
            overall_score = (quality_score * 0.4 + translation_confidence * 0.4 + ocr_quality * 0.2)
            
            translated_data['enhanced_metrics'] = {
                'ocr_quality': ocr_quality,
                'parsing_quality': quality_score,
                'translation_quality': translation_confidence,
                'overall_score': overall_score,
                'total_ocr_elements': len(ocr_results),
                'extraction_methods': parsed_data.get('extraction_metadata', {}).get('extraction_methods', []),
                'translation_methods': translated_data.get('translation_metadata', {}).get('translation_methods_used', [])
            }
            
            logger.info(f"Enhanced processing completed for {image_path.name}")
            logger.info(f"Overall score: {overall_score:.2f} (OCR: {ocr_quality:.2f}, Parsing: {quality_score:.2f}, Translation: {translation_confidence:.2f})")
            
            return translated_data
            
        except Exception as e:
            logger.error(f"Failed to process {image_path.name}: {e}")
            logger.error(traceback.format_exc())
            
            return {
                'file_info': {
                    'file_path': str(image_path),
                    'file_name': image_path.name,
                    'processing_timestamp': datetime.now().isoformat(),
                    'processing_method': 'enhanced_ai_pipeline'
                },
                'error': str(e),
                'error_traceback': traceback.format_exc(),
                'enhanced_metrics': {
                    'overall_score': 0.0,
                    'processing_failed': True
                }
            }
    
    def process_all_cards(self, data_directory: Path = None) -> List[Dict]:
        """Process all ID cards with enhanced AI pipeline"""
        if data_directory is None:
            data_directory = DATA_DIR
        
        logger.info(f"Starting enhanced AI batch processing of ID cards in: {data_directory}")
        
        # Get all image files
        image_files = self.get_image_files(data_directory)
        
        if not image_files:
            logger.warning(f"No image files found in {data_directory}")
            return []
        
        # Process each image with enhanced pipeline
        results = []
        successful_count = 0
        failed_count = 0
        total_score = 0.0
        
        for i, image_path in enumerate(image_files, 1):
            logger.info(f"Processing file {i}/{len(image_files)}: {image_path.name}")
            
            result = self.process_single_card(image_path)
            results.append(result)
            
            if 'error' in result:
                failed_count += 1
            else:
                successful_count += 1
                overall_score = result.get('enhanced_metrics', {}).get('overall_score', 0)
                total_score += overall_score
            
            # Log progress with enhanced metrics
            avg_score = total_score / successful_count if successful_count > 0 else 0
            logger.info(f"Progress: {i}/{len(image_files)} files processed "
                       f"({successful_count} successful, {failed_count} failed, avg score: {avg_score:.2f})")
        
        logger.info(f"Enhanced batch processing completed: {successful_count} successful, {failed_count} failed")
        if successful_count > 0:
            logger.info(f"Average quality score: {total_score / successful_count:.2f}")
        
        return results
    
    def save_enhanced_results(self, results: List[Dict], base_filename: str = None) -> Dict:
        """Save enhanced processing results with detailed analytics"""
        if base_filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_filename = f"enhanced_tunisian_id_cards_{timestamp}"
        
        logger.info("Saving enhanced results with detailed analytics")
        
        # Filter successful results for main data export
        successful_results = [r for r in results if 'error' not in r]
        
        # Save main data
        saved_files = {}
        if successful_results:
            saved_files = self.data_saver.save_all_formats(successful_results, base_filename)
        
        # Create enhanced processing report
        enhanced_report = self._create_enhanced_report(results)
        report_filename = f"{base_filename}_enhanced_report.json"
        report_path = self.data_saver.save_json(enhanced_report, report_filename)
        saved_files['enhanced_report'] = report_path
        
        logger.info(f"Enhanced results saved in {len(saved_files)} files")
        return saved_files
    
    def _create_enhanced_report(self, results: List[Dict]) -> Dict:
        """Create detailed enhanced processing report"""
        successful_results = [r for r in results if 'error' not in r]
        failed_results = [r for r in results if 'error' in r]
        
        # Calculate detailed statistics
        total_files = len(results)
        successful_files = len(successful_results)
        
        # OCR statistics
        ocr_scores = [r.get('enhanced_metrics', {}).get('ocr_quality', 0) for r in successful_results]
        parsing_scores = [r.get('enhanced_metrics', {}).get('parsing_quality', 0) for r in successful_results]
        translation_scores = [r.get('enhanced_metrics', {}).get('translation_quality', 0) for r in successful_results]
        overall_scores = [r.get('enhanced_metrics', {}).get('overall_score', 0) for r in successful_results]
        
        # Method usage statistics
        extraction_methods = {}
        translation_methods = {}
        
        for result in successful_results:
            methods = result.get('enhanced_metrics', {}).get('extraction_methods', [])
            for method in methods:
                extraction_methods[method] = extraction_methods.get(method, 0) + 1
            
            trans_methods = result.get('enhanced_metrics', {}).get('translation_methods', [])
            for method in trans_methods:
                translation_methods[method] = translation_methods.get(method, 0) + 1
        
        enhanced_report = {
            'enhanced_processing_summary': {
                'total_files': total_files,
                'successful_files': successful_files,
                'failed_files': len(failed_results),
                'success_rate': (successful_files / total_files * 100) if total_files > 0 else 0,
                'processing_timestamp': datetime.now().isoformat(),
                'ai_models_used': ['AdvancedOCR', 'IntelligentParser', 'EnhancedTranslator']
            },
            'quality_metrics': {
                'average_ocr_quality': sum(ocr_scores) / len(ocr_scores) if ocr_scores else 0,
                'average_parsing_quality': sum(parsing_scores) / len(parsing_scores) if parsing_scores else 0,
                'average_translation_quality': sum(translation_scores) / len(translation_scores) if translation_scores else 0,
                'average_overall_score': sum(overall_scores) / len(overall_scores) if overall_scores else 0,
                'min_overall_score': min(overall_scores) if overall_scores else 0,
                'max_overall_score': max(overall_scores) if overall_scores else 0
            },
            'method_usage_statistics': {
                'extraction_methods': extraction_methods,
                'translation_methods': translation_methods
            },
            'file_details': [
                {
                    'file': result.get('file_info', {}).get('file_name', 'unknown'),
                    'overall_score': result.get('enhanced_metrics', {}).get('overall_score', 0),
                    'ocr_quality': result.get('enhanced_metrics', {}).get('ocr_quality', 0),
                    'parsing_quality': result.get('enhanced_metrics', {}).get('parsing_quality', 0),
                    'translation_quality': result.get('enhanced_metrics', {}).get('translation_quality', 0),
                    'extraction_methods': result.get('enhanced_metrics', {}).get('extraction_methods', []),
                    'translation_methods': result.get('enhanced_metrics', {}).get('translation_methods', [])
                }
                for result in successful_results
            ],
            'error_summary': [
                {
                    'file': result.get('file_info', {}).get('file_name', 'unknown'),
                    'error': result.get('error', 'Unknown error')
                }
                for result in failed_results
            ]
        }
        
        return enhanced_report
    
    def run_enhanced_pipeline(self, data_directory: Path = None) -> Dict:
        """Run the complete enhanced AI pipeline"""
        logger.info("=" * 80)
        logger.info("STARTING ENHANCED AI TUNISIAN ID CARD PROCESSING PIPELINE")
        logger.info("Using state-of-the-art OCR, NLP, and Translation models")
        logger.info("=" * 80)
        
        start_time = datetime.now()
        
        try:
            # Process all cards with enhanced AI
            results = self.process_all_cards(data_directory)
            
            # Save enhanced results
            saved_files = self.save_enhanced_results(results)
            
            # Calculate final enhanced statistics
            total_files = len(results)
            successful_files = len([r for r in results if 'error' not in r])
            failed_files = total_files - successful_files
            
            # Calculate average scores
            successful_results = [r for r in results if 'error' not in r]
            avg_overall_score = 0
            if successful_results:
                scores = [r.get('enhanced_metrics', {}).get('overall_score', 0) for r in successful_results]
                avg_overall_score = sum(scores) / len(scores)
            
            end_time = datetime.now()
            processing_time = end_time - start_time
            
            # Create final enhanced summary
            summary = {
                'enhanced_pipeline_summary': {
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat(),
                    'processing_time_seconds': processing_time.total_seconds(),
                    'total_files': total_files,
                    'successful_files': successful_files,
                    'failed_files': failed_files,
                    'success_rate': (successful_files / total_files * 100) if total_files > 0 else 0,
                    'average_quality_score': avg_overall_score,
                    'ai_models_used': ['AdvancedOCR', 'IntelligentParser', 'EnhancedTranslator']
                },
                'saved_files': {str(k): str(v) for k, v in saved_files.items()},
                'results': results
            }
            
            logger.info("=" * 80)
            logger.info("ENHANCED AI PIPELINE COMPLETED SUCCESSFULLY")
            logger.info(f"Processed {total_files} files in {processing_time}")
            logger.info(f"Success rate: {summary['enhanced_pipeline_summary']['success_rate']:.1f}%")
            logger.info(f"Average quality score: {avg_overall_score:.2f}")
            logger.info("=" * 80)
            
            return summary
            
        except Exception as e:
            logger.error(f"Enhanced pipeline failed: {e}")
            logger.error(traceback.format_exc())
            raise

def main():
    """Main entry point for enhanced processing"""
    try:
        # Initialize enhanced processor
        processor = EnhancedIDCardProcessor()
        
        # Run the enhanced pipeline
        summary = processor.run_enhanced_pipeline()
        
        # Print enhanced summary to console
        print("\n" + "=" * 80)
        print("ENHANCED AI PROCESSING SUMMARY")
        print("=" * 80)
        print(f"Total files processed: {summary['enhanced_pipeline_summary']['total_files']}")
        print(f"Successful extractions: {summary['enhanced_pipeline_summary']['successful_files']}")
        print(f"Failed extractions: {summary['enhanced_pipeline_summary']['failed_files']}")
        print(f"Success rate: {summary['enhanced_pipeline_summary']['success_rate']:.1f}%")
        print(f"Average quality score: {summary['enhanced_pipeline_summary']['average_quality_score']:.2f}")
        print(f"Processing time: {summary['enhanced_pipeline_summary']['processing_time_seconds']:.1f} seconds")
        print(f"AI models used: {', '.join(summary['enhanced_pipeline_summary']['ai_models_used'])}")
        print("\nSaved files:")
        for file_type, file_path in summary['saved_files'].items():
            print(f"  {file_type}: {file_path}")
        print("=" * 80)
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("Enhanced processing interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Enhanced application failed: {e}")
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())
