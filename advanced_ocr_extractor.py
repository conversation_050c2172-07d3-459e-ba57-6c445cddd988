"""
Advanced OCR Extractor with multiple intelligent models
Uses PaddleOCR, TrOCR, and enhanced preprocessing for better results
"""

import cv2
import numpy as np
import pytesseract
import easyocr
from PIL import Image, ImageEnhance, ImageFilter
import logging
from pathlib import Path
import uuid
from typing import List, Dict, Tuple, Optional
import re
import json

try:
    from paddleocr import PaddleOCR
    PADDLE_AVAILABLE = True
except ImportError:
    PADDLE_AVAILABLE = False
    logging.warning("PaddleOCR not available. Install with: pip install paddlepaddle paddleocr")

try:
    from transformers import TrOCRProcessor, VisionEncoderDecoderModel
    import torch
    TROCR_AVAILABLE = True
except ImportError:
    TROCR_AVAILABLE = False
    logging.warning("TrOCR not available.")

from config import OCR_CONFIG, ANALYSIS_OUTPUT_DIR

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedOCRExtractor:
    """Advanced OCR extractor with multiple intelligent models"""
    
    def __init__(self):
        """Initialize all available OCR engines"""
        self.engines = {}
        
        # Initialize EasyOCR
        try:
            self.engines['easyocr'] = easyocr.Reader(['ar', 'en'], gpu=False)
            logger.info("EasyOCR initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize EasyOCR: {e}")
        
        # Initialize PaddleOCR
        if PADDLE_AVAILABLE:
            try:
                self.engines['paddle'] = PaddleOCR(use_angle_cls=True, lang='ar', use_gpu=False)
                logger.info("PaddleOCR initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize PaddleOCR: {e}")
        
        # Initialize TrOCR
        if TROCR_AVAILABLE:
            try:
                self.trocr_processor = TrOCRProcessor.from_pretrained('microsoft/trocr-base-printed')
                self.trocr_model = VisionEncoderDecoderModel.from_pretrained('microsoft/trocr-base-printed')
                self.engines['trocr'] = True
                logger.info("TrOCR initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize TrOCR: {e}")
        
        # Test Tesseract
        try:
            pytesseract.get_tesseract_version()
            self.engines['tesseract'] = True
            logger.info("Tesseract initialized successfully")
        except Exception as e:
            logger.error(f"Tesseract not available: {e}")
    
    def advanced_preprocessing(self, image_path: str) -> List[np.ndarray]:
        """
        Advanced preprocessing with multiple enhancement techniques
        Returns multiple processed versions for different OCR engines
        """
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Could not load image: {image_path}")
        
        processed_images = []
        process_id = str(uuid.uuid4())
        
        # Original grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 1. High contrast version for Tesseract
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        high_contrast = clahe.apply(gray)
        
        # Resize for better OCR
        height, width = high_contrast.shape
        scale_factor = max(2.0, 1000 / min(height, width))
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        
        tesseract_img = cv2.resize(high_contrast, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
        
        # Apply morphological operations
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        tesseract_img = cv2.morphologyEx(tesseract_img, cv2.MORPH_CLOSE, kernel)
        
        processed_images.append(('tesseract', tesseract_img))
        
        # 2. Denoised version for EasyOCR
        denoised = cv2.bilateralFilter(gray, 9, 75, 75)
        easyocr_img = cv2.resize(denoised, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
        processed_images.append(('easyocr', easyocr_img))
        
        # 3. Enhanced version for PaddleOCR
        if PADDLE_AVAILABLE:
            # PaddleOCR works well with color images
            enhanced_color = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            processed_images.append(('paddle', enhanced_color))
        
        # 4. Clean version for TrOCR
        if TROCR_AVAILABLE:
            # TrOCR expects PIL Image
            pil_img = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            pil_img = pil_img.resize((new_width, new_height), Image.LANCZOS)
            
            # Enhance for TrOCR
            enhancer = ImageEnhance.Contrast(pil_img)
            pil_img = enhancer.enhance(1.5)
            enhancer = ImageEnhance.Sharpness(pil_img)
            pil_img = enhancer.enhance(1.2)
            
            processed_images.append(('trocr', pil_img))
        
        # Save analysis images
        for engine_name, img in processed_images:
            if isinstance(img, np.ndarray):
                cv2.imwrite(str(ANALYSIS_OUTPUT_DIR / f"{process_id}_{engine_name}_processed.jpg"), img)
        
        logger.info(f"Advanced preprocessing completed. Generated {len(processed_images)} optimized images")
        return processed_images
    
    def extract_with_tesseract(self, image: np.ndarray) -> List[Dict]:
        """Enhanced Tesseract extraction with multiple configurations"""
        if 'tesseract' not in self.engines:
            return []
        
        results = []
        
        # Multiple Tesseract configurations for better results
        configs = [
            '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyzأبتثجحخدذرزسشصضطظعغفقكلمنهويءآإؤئةى',
            '--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyzأبتثجحخدذرزسشصضطظعغفقكلمنهويءآإؤئةى',
            '--oem 3 --psm 7 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyzأبتثجحخدذرزسشصضطظعغفقكلمنهويءآإؤئةى',
            '--oem 3 --psm 13'  # Raw line for better Arabic
        ]
        
        for config in configs:
            try:
                data = pytesseract.image_to_data(
                    image,
                    lang='ara+eng',
                    config=config,
                    output_type=pytesseract.Output.DICT
                )
                
                n_boxes = len(data['level'])
                for i in range(n_boxes):
                    confidence = int(data['conf'][i])
                    text = data['text'][i].strip()
                    
                    if confidence > 20 and len(text) > 1:
                        x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                        
                        results.append({
                            'text': text,
                            'confidence': confidence / 100.0,
                            'bbox': [x, y, x + w, y + h],
                            'engine': f'tesseract_{config[:10]}'
                        })
            except Exception as e:
                logger.warning(f"Tesseract config failed: {e}")
        
        return results
    
    def extract_with_paddle(self, image: np.ndarray) -> List[Dict]:
        """Extract text using PaddleOCR"""
        if 'paddle' not in self.engines:
            return []
        
        try:
            result = self.engines['paddle'].ocr(image, cls=True)
            
            formatted_results = []
            if result and result[0]:
                for line in result[0]:
                    if len(line) >= 2:
                        bbox_points = line[0]
                        text_info = line[1]
                        
                        if isinstance(text_info, tuple) and len(text_info) >= 2:
                            text = text_info[0]
                            confidence = text_info[1]
                            
                            # Convert bbox to [x1, y1, x2, y2] format
                            x_coords = [point[0] for point in bbox_points]
                            y_coords = [point[1] for point in bbox_points]
                            x1, y1, x2, y2 = min(x_coords), min(y_coords), max(x_coords), max(y_coords)
                            
                            formatted_results.append({
                                'text': text.strip(),
                                'confidence': confidence,
                                'bbox': [int(x1), int(y1), int(x2), int(y2)],
                                'engine': 'paddleocr'
                            })
            
            logger.info(f"PaddleOCR extracted {len(formatted_results)} text elements")
            return formatted_results
            
        except Exception as e:
            logger.error(f"PaddleOCR failed: {e}")
            return []
    
    def extract_with_trocr(self, image: Image.Image) -> List[Dict]:
        """Extract text using TrOCR (for printed text)"""
        if 'trocr' not in self.engines:
            return []
        
        try:
            # TrOCR processes the entire image as one text block
            pixel_values = self.trocr_processor(image, return_tensors="pt").pixel_values
            generated_ids = self.trocr_model.generate(pixel_values)
            generated_text = self.trocr_processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
            
            if generated_text.strip():
                return [{
                    'text': generated_text.strip(),
                    'confidence': 0.8,  # TrOCR doesn't provide confidence scores
                    'bbox': [0, 0, image.width, image.height],
                    'engine': 'trocr'
                }]
            
        except Exception as e:
            logger.error(f"TrOCR failed: {e}")
        
        return []
    
    def intelligent_text_fusion(self, all_results: List[Dict]) -> List[Dict]:
        """
        Intelligent fusion of results from multiple OCR engines
        Uses advanced algorithms to combine and validate results
        """
        if not all_results:
            return []
        
        # Group similar results
        grouped_results = []
        used_indices = set()
        
        for i, result1 in enumerate(all_results):
            if i in used_indices:
                continue
            
            group = [result1]
            used_indices.add(i)
            
            for j, result2 in enumerate(all_results[i+1:], i+1):
                if j in used_indices:
                    continue
                
                # Check if results are similar (same region or similar text)
                if self._are_results_similar(result1, result2):
                    group.append(result2)
                    used_indices.add(j)
            
            # Fuse the group into best result
            fused_result = self._fuse_result_group(group)
            if fused_result:
                grouped_results.append(fused_result)
        
        # Sort by confidence and position
        grouped_results.sort(key=lambda x: (-x['confidence'], x['bbox'][1], x['bbox'][0]))
        
        logger.info(f"Intelligent fusion: {len(all_results)} → {len(grouped_results)} results")
        return grouped_results
    
    def _are_results_similar(self, result1: Dict, result2: Dict) -> bool:
        """Check if two OCR results are similar"""
        # Check bbox overlap
        bbox_overlap = self._calculate_bbox_overlap(result1['bbox'], result2['bbox'])
        
        # Check text similarity
        text_similarity = self._calculate_text_similarity(result1['text'], result2['text'])
        
        return bbox_overlap > 0.3 or text_similarity > 0.6
    
    def _fuse_result_group(self, group: List[Dict]) -> Optional[Dict]:
        """Fuse a group of similar results into the best one"""
        if not group:
            return None
        
        # Choose result with highest confidence
        best_result = max(group, key=lambda x: x['confidence'])
        
        # If multiple engines agree on similar text, boost confidence
        if len(group) > 1:
            texts = [r['text'] for r in group]
            if len(set(texts)) == 1:  # All engines agree
                best_result['confidence'] = min(1.0, best_result['confidence'] * 1.2)
                best_result['engine'] = f"consensus_{len(group)}_engines"
        
        return best_result
    
    def _calculate_bbox_overlap(self, bbox1: List[int], bbox2: List[int]) -> float:
        """Calculate overlap ratio between two bounding boxes"""
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2
        
        # Calculate intersection
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
        
        intersection_area = (x2_i - x1_i) * (y2_i - y1_i)
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union_area = area1 + area2 - intersection_area
        
        return intersection_area / union_area if union_area > 0 else 0.0
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two text strings"""
        if not text1 or not text2:
            return 0.0
        
        # Normalize texts
        text1 = text1.lower().strip()
        text2 = text2.lower().strip()
        
        # Exact match
        if text1 == text2:
            return 1.0
        
        # Character-based similarity
        set1, set2 = set(text1), set(text2)
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0
    
    def extract_text(self, image_path: str) -> List[Dict]:
        """
        Main method to extract text using all available engines
        """
        logger.info(f"Starting advanced OCR extraction for: {image_path}")
        
        # Advanced preprocessing
        processed_images = self.advanced_preprocessing(image_path)
        
        all_results = []
        
        # Extract with each engine
        for engine_name, processed_img in processed_images:
            if engine_name == 'tesseract' and isinstance(processed_img, np.ndarray):
                results = self.extract_with_tesseract(processed_img)
                all_results.extend(results)
            
            elif engine_name == 'easyocr' and isinstance(processed_img, np.ndarray):
                if 'easyocr' in self.engines:
                    try:
                        results = self.engines['easyocr'].readtext(processed_img, detail=1)
                        for bbox, text, confidence in results:
                            x_coords = [point[0] for point in bbox]
                            y_coords = [point[1] for point in bbox]
                            x1, y1, x2, y2 = min(x_coords), min(y_coords), max(x_coords), max(y_coords)
                            
                            all_results.append({
                                'text': text.strip(),
                                'confidence': confidence,
                                'bbox': [int(x1), int(y1), int(x2), int(y2)],
                                'engine': 'easyocr_advanced'
                            })
                    except Exception as e:
                        logger.error(f"EasyOCR advanced failed: {e}")
            
            elif engine_name == 'paddle' and isinstance(processed_img, np.ndarray):
                results = self.extract_with_paddle(processed_img)
                all_results.extend(results)
            
            elif engine_name == 'trocr' and isinstance(processed_img, Image.Image):
                results = self.extract_with_trocr(processed_img)
                all_results.extend(results)
        
        # Intelligent fusion of all results
        final_results = self.intelligent_text_fusion(all_results)
        
        logger.info(f"Advanced OCR extraction completed. Found {len(final_results)} high-quality text elements")
        return final_results
