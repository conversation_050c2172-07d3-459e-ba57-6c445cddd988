"""
Enhanced Arabic to French Translator with multiple AI models
Uses state-of-the-art translation models and intelligent validation
"""

import logging
import re
from typing import Dict, Optional, List, Tuple
import unicodedata

try:
    from transformers import (
        MarianMTModel, MarianTokenizer, 
        M2M100ForConditionalGeneration, M2M100Tokenizer,
        pipeline, AutoTokenizer, AutoModelForSeq2SeqLM
    )
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    logging.warning("Transformers library not available.")

try:
    from googletrans import Translator as GoogleTranslator
    GOOGLETRANS_AVAILABLE = True
except ImportError:
    GOOGLETRANS_AVAILABLE = False
    logging.warning("Google Translate library not available.")

from config import TRANSLATION_CONFIG

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedArabicTranslator:
    """Enhanced Arabic to French translator with multiple AI models"""
    
    def __init__(self):
        """Initialize multiple translation models"""
        self.models = {}
        self.google_translator = None
        
        # Initialize multiple transformer models
        if TRANSFORMERS_AVAILABLE:
            self._load_transformer_models()
        
        # Initialize Google Translator as backup
        if GOOGLETRANS_AVAILABLE:
            try:
                self.google_translator = GoogleTranslator()
                logger.info("Google Translator initialized as backup")
            except Exception as e:
                logger.error(f"Failed to initialize Google Translator: {e}")
        
        # Enhanced dictionaries for Tunisian context
        self.enhanced_name_dictionary = {
            # Common Tunisian first names with variations
            'محمد': 'Mohamed', 'محمّد': 'Mohamed', 'مُحمد': 'Mohamed',
            'أحمد': 'Ahmed', 'احمد': 'Ahmed', 'أحمّد': 'Ahmed',
            'علي': 'Ali', 'عليّ': 'Ali', 'عَلي': 'Ali',
            'حسن': 'Hassan', 'حسّن': 'Hassan', 'الحسن': 'Hassan',
            'حسين': 'Hussein', 'حسّين': 'Hussein', 'الحسين': 'Hussein',
            'عبد الله': 'Abdallah', 'عبدالله': 'Abdallah', 'عبد اللّه': 'Abdallah',
            'عبد الرحمن': 'Abderrahman', 'عبدالرحمن': 'Abderrahman',
            'عبد العزيز': 'Abdelaziz', 'عبدالعزيز': 'Abdelaziz',
            'عبد الرؤوف': 'Abderraouf', 'عبدالرؤوف': 'Abderraouf',
            'عبد الحميد': 'Abdelhamid', 'عبدالحميد': 'Abdelhamid',
            'فاطمة': 'Fatma', 'فاطمه': 'Fatma', 'فطيمة': 'Fatma',
            'عائشة': 'Aicha', 'عايشة': 'Aicha', 'عائشه': 'Aicha',
            'خديجة': 'Khadija', 'خديجه': 'Khadija', 'خديجا': 'Khadija',
            'زينب': 'Zeineb', 'زينب': 'Zeineb', 'زينبة': 'Zeineb',
            'مريم': 'Maryam', 'مريام': 'Maryam', 'مريمة': 'Maryam',
            'سارة': 'Sara', 'ساره': 'Sara', 'سارا': 'Sara',
            'ليلى': 'Leila', 'ليلا': 'Leila', 'ليله': 'Leila',
            'نور': 'Nour', 'نوّر': 'Nour', 'النور': 'Nour',
            'أمينة': 'Amina', 'امينة': 'Amina', 'أمينه': 'Amina',
            'سلمى': 'Salma', 'سلما': 'Salma', 'سلمه': 'Salma',
            
            # Common Tunisian last names
            'بن علي': 'Ben Ali', 'ابن علي': 'Ben Ali', 'بن عليّ': 'Ben Ali',
            'التونسي': 'Ettounsi', 'التونسيّ': 'Ettounsi', 'تونسي': 'Ettounsi',
            'الصغير': 'Esseghir', 'صغير': 'Esseghir', 'الصّغير': 'Esseghir',
            'الكبير': 'Elkebir', 'كبير': 'Elkebir', 'الكبّير': 'Elkebir',
            'بن عمر': 'Ben Omar', 'ابن عمر': 'Ben Omar', 'بن عمّر': 'Ben Omar',
            'بن محمد': 'Ben Mohamed', 'ابن محمد': 'Ben Mohamed',
            'الهادي': 'Elhadi', 'هادي': 'Elhadi', 'الهاديّ': 'Elhadi',
            'النوري': 'Enouri', 'نوري': 'Enouri', 'النوريّ': 'Enouri',
            'الزهراني': 'Ezahrani', 'زهراني': 'Ezahrani',
            'القادري': 'Elkadri', 'قادري': 'Elkadri', 'القادريّ': 'Elkadri',
            'بن سالم': 'Ben Salem', 'ابن سالم': 'Ben Salem',
            'بن يوسف': 'Ben Youssef', 'ابن يوسف': 'Ben Youssef',
        }
        
        self.enhanced_place_dictionary = {
            # Tunisian governorates and major cities with variations
            'تونس': 'Tunis', 'تونس العاصمة': 'Tunis', 'مدينة تونس': 'Tunis',
            'صفاقس': 'Sfax', 'صفاقص': 'Sfax', 'مدينة صفاقس': 'Sfax',
            'سوسة': 'Sousse', 'سوسه': 'Sousse', 'مدينة سوسة': 'Sousse',
            'القيروان': 'Kairouan', 'قيروان': 'Kairouan', 'مدينة القيروان': 'Kairouan',
            'بنزرت': 'Bizerte', 'بنزرته': 'Bizerte', 'مدينة بنزرت': 'Bizerte',
            'قابس': 'Gabès', 'قابص': 'Gabès', 'مدينة قابس': 'Gabès',
            'أريانة': 'Ariana', 'اريانة': 'Ariana', 'مدينة أريانة': 'Ariana',
            'منوبة': 'Manouba', 'منوبه': 'Manouba', 'مدينة منوبة': 'Manouba',
            'بن عروس': 'Ben Arous', 'ابن عروس': 'Ben Arous',
            'نابل': 'Nabeul', 'نابل': 'Nabeul', 'مدينة نابل': 'Nabeul',
            'زغوان': 'Zaghouan', 'زغوان': 'Zaghouan', 'مدينة زغوان': 'Zaghouan',
            'باجة': 'Béja', 'باجه': 'Béja', 'مدينة باجة': 'Béja',
            'جندوبة': 'Jendouba', 'جندوبه': 'Jendouba', 'مدينة جندوبة': 'Jendouba',
            'الكاف': 'Le Kef', 'كاف': 'Le Kef', 'مدينة الكاف': 'Le Kef',
            'سليانة': 'Siliana', 'سليانه': 'Siliana', 'مدينة سليانة': 'Siliana',
            'القصرين': 'Kasserine', 'قصرين': 'Kasserine', 'مدينة القصرين': 'Kasserine',
            'سيدي بوزيد': 'Sidi Bouzid', 'سيدي بو زيد': 'Sidi Bouzid',
            'قفصة': 'Gafsa', 'قفصه': 'Gafsa', 'مدينة قفصة': 'Gafsa',
            'توزر': 'Tozeur', 'توزر': 'Tozeur', 'مدينة توزر': 'Tozeur',
            'قبلي': 'Kebili', 'قبليّ': 'Kebili', 'مدينة قبلي': 'Kebili',
            'تطاوين': 'Tataouine', 'تطاوين': 'Tataouine', 'مدينة تطاوين': 'Tataouine',
            'مدنين': 'Medenine', 'مدنين': 'Medenine', 'مدينة مدنين': 'Medenine',
            'المهدية': 'Mahdia', 'مهدية': 'Mahdia', 'مدينة المهدية': 'Mahdia',
            'المنستير': 'Monastir', 'منستير': 'Monastir', 'مدينة المنستير': 'Monastir',
        }
        
        self.enhanced_general_dictionary = {
            # Nationality and status
            'تونسي': 'Tunisien', 'تونسيّ': 'Tunisien', 'التونسي': 'Tunisien',
            'تونسية': 'Tunisienne', 'تونسيّة': 'Tunisienne', 'التونسية': 'Tunisienne',
            'الجمهورية التونسية': 'République Tunisienne',
            'الجمهوريةالتونسية': 'République Tunisienne',
            'الجمهوريةالقونسي': 'République Tunisienne',  # OCR error correction
            
            # Gender and marital status
            'ذكر': 'Masculin', 'ذكّر': 'Masculin', 'الذكر': 'Masculin',
            'أنثى': 'Féminin', 'انثى': 'Féminin', 'الأنثى': 'Féminin',
            'أعزب': 'Célibataire', 'اعزب': 'Célibataire', 'الأعزب': 'Célibataire',
            'متزوج': 'Marié', 'متزوّج': 'Marié', 'المتزوج': 'Marié',
            'متزوجة': 'Mariée', 'متزوّجة': 'Mariée', 'المتزوجة': 'Mariée',
            'مطلق': 'Divorcé', 'مطلّق': 'Divorcé', 'المطلق': 'Divorcé',
            'مطلقة': 'Divorcée', 'مطلّقة': 'Divorcée', 'المطلقة': 'Divorcée',
            'أرمل': 'Veuf', 'ارمل': 'Veuf', 'الأرمل': 'Veuf',
            'أرملة': 'Veuve', 'ارملة': 'Veuve', 'الأرملة': 'Veuve',
            
            # Document types
            'بطاقة التعريف': 'Carte d\'identité',
            'بطاقه التعريف': 'Carte d\'identité',
            'بطاقة التعريف الوطنية': 'Carte d\'identité nationale',
            'بطاقه التعريف الوما': 'Carte d\'identité nationale',  # OCR error correction
            
            # Common address terms
            'شارع': 'Rue', 'شارع': 'Rue', 'الشارع': 'Rue',
            'نهج': 'Rue', 'النهج': 'Rue',
            'طريق': 'Route', 'الطريق': 'Route',
            'حي': 'Quartier', 'الحي': 'Quartier',
            'منطقة': 'Zone', 'المنطقة': 'Zone',
        }
    
    def _load_transformer_models(self):
        """Load multiple transformer models for better translation"""
        try:
            # Primary model: Helsinki Arabic to French
            self.models['helsinki_ar_fr'] = {
                'tokenizer': MarianTokenizer.from_pretrained('Helsinki-NLP/opus-mt-ar-fr'),
                'model': MarianMTModel.from_pretrained('Helsinki-NLP/opus-mt-ar-fr')
            }
            logger.info("Helsinki AR-FR model loaded successfully")
        except Exception as e:
            logger.warning(f"Failed to load Helsinki AR-FR model: {e}")
        
        try:
            # Secondary model: M2M100 for multilingual translation
            self.models['m2m100'] = {
                'tokenizer': M2M100Tokenizer.from_pretrained('facebook/m2m100_418M'),
                'model': M2M100ForConditionalGeneration.from_pretrained('facebook/m2m100_418M')
            }
            logger.info("M2M100 model loaded successfully")
        except Exception as e:
            logger.warning(f"Failed to load M2M100 model: {e}")
        
        try:
            # Backup model: Helsinki Arabic to English
            self.models['helsinki_ar_en'] = {
                'tokenizer': MarianTokenizer.from_pretrained('Helsinki-NLP/opus-mt-ar-en'),
                'model': MarianMTModel.from_pretrained('Helsinki-NLP/opus-mt-ar-en')
            }
            logger.info("Helsinki AR-EN model loaded successfully")
        except Exception as e:
            logger.warning(f"Failed to load Helsinki AR-EN model: {e}")
    
    def _clean_arabic_text(self, text: str) -> str:
        """Enhanced Arabic text cleaning"""
        if not text:
            return ""
        
        # Remove diacritics
        text = ''.join(c for c in unicodedata.normalize('NFD', text) 
                      if unicodedata.category(c) != 'Mn')
        
        # Normalize Arabic characters
        text = text.replace('ة', 'ه')  # Taa marbuta to haa
        text = text.replace('ى', 'ي')  # Alif maksura to yaa
        text = text.replace('إ', 'ا')  # Alif with hamza to alif
        text = text.replace('أ', 'ا')  # Alif with hamza to alif
        text = text.replace('آ', 'ا')  # Alif madda to alif
        text = text.replace('ؤ', 'و')  # Waw with hamza to waw
        text = text.replace('ئ', 'ي')  # Yaa with hamza to yaa
        
        # Remove extra whitespace and punctuation
        text = re.sub(r'[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF'
                     r'A-Za-z0-9\s\-\.\,\(\)]', '', text)
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def _translate_with_helsinki_ar_fr(self, text: str) -> Optional[str]:
        """Translate using Helsinki Arabic to French model"""
        if 'helsinki_ar_fr' not in self.models:
            return None
        
        try:
            model_data = self.models['helsinki_ar_fr']
            inputs = model_data['tokenizer'](text, return_tensors="pt", padding=True, truncation=True, max_length=512)
            
            outputs = model_data['model'].generate(
                **inputs,
                max_length=512,
                temperature=0.1,
                do_sample=False,
                pad_token_id=model_data['tokenizer'].pad_token_id
            )
            
            translation = model_data['tokenizer'].decode(outputs[0], skip_special_tokens=True)
            return translation.strip()
            
        except Exception as e:
            logger.error(f"Helsinki AR-FR translation failed: {e}")
            return None
    
    def _translate_with_m2m100(self, text: str) -> Optional[str]:
        """Translate using M2M100 model"""
        if 'm2m100' not in self.models:
            return None
        
        try:
            model_data = self.models['m2m100']
            model_data['tokenizer'].src_lang = "ar"
            
            encoded_ar = model_data['tokenizer'](text, return_tensors="pt", padding=True, truncation=True)
            generated_tokens = model_data['model'].generate(
                **encoded_ar,
                forced_bos_token_id=model_data['tokenizer'].get_lang_id("fr"),
                max_length=512
            )
            
            translation = model_data['tokenizer'].batch_decode(generated_tokens, skip_special_tokens=True)[0]
            return translation.strip()
            
        except Exception as e:
            logger.error(f"M2M100 translation failed: {e}")
            return None
    
    def _translate_with_enhanced_dictionary(self, text: str, field_type: str = None) -> Optional[str]:
        """Enhanced dictionary translation with fuzzy matching"""
        text = text.strip()
        
        # Direct lookup in appropriate dictionary
        all_dicts = {
            **self.enhanced_name_dictionary,
            **self.enhanced_place_dictionary,
            **self.enhanced_general_dictionary
        }
        
        # Exact match
        if text in all_dicts:
            return all_dicts[text]
        
        # Field-specific lookup
        if field_type in ['first_name', 'last_name']:
            if text in self.enhanced_name_dictionary:
                return self.enhanced_name_dictionary[text]
        elif field_type in ['birth_place', 'address']:
            if text in self.enhanced_place_dictionary:
                return self.enhanced_place_dictionary[text]
        
        # Fuzzy matching for close matches
        import difflib
        
        best_match = None
        best_ratio = 0.0
        
        for arabic_text, french_text in all_dicts.items():
            ratio = difflib.SequenceMatcher(None, text, arabic_text).ratio()
            if ratio > best_ratio and ratio > 0.8:  # High similarity threshold
                best_ratio = ratio
                best_match = french_text
        
        if best_match:
            logger.info(f"Fuzzy match found: '{text}' → '{best_match}' (similarity: {best_ratio:.2f})")
            return best_match
        
        # Partial matching for compound words
        for arabic_text, french_text in all_dicts.items():
            if arabic_text in text or text in arabic_text:
                return text.replace(arabic_text, french_text)
        
        return None
    
    def translate_field(self, text: str, field_type: str = None, confidence: float = 1.0) -> Dict:
        """
        Enhanced field translation with multiple models and validation
        """
        if not text or not text.strip():
            return {
                'original': text,
                'translated': text,
                'translation_method': 'no_translation_needed',
                'translation_confidence': 1.0,
                'field_type': field_type
            }
        
        cleaned_text = self._clean_arabic_text(text)
        
        # If no Arabic characters, return as is
        if not self._is_arabic_text(cleaned_text):
            return {
                'original': text,
                'translated': text,
                'translation_method': 'no_translation_needed',
                'translation_confidence': 1.0,
                'field_type': field_type
            }
        
        translation_candidates = []
        
        # Try enhanced dictionary first (highest priority for names/places)
        dict_translation = self._translate_with_enhanced_dictionary(cleaned_text, field_type)
        if dict_translation:
            translation_candidates.append(('enhanced_dictionary', dict_translation, 0.95))
        
        # Try Helsinki AR-FR model
        helsinki_translation = self._translate_with_helsinki_ar_fr(cleaned_text)
        if helsinki_translation:
            translation_candidates.append(('helsinki_ar_fr', helsinki_translation, 0.85))
        
        # Try M2M100 model
        m2m_translation = self._translate_with_m2m100(cleaned_text)
        if m2m_translation:
            translation_candidates.append(('m2m100', m2m_translation, 0.80))
        
        # Try Google Translate as backup
        if self.google_translator:
            try:
                google_translation = self.google_translator.translate(cleaned_text, src='ar', dest='fr').text
                if google_translation:
                    translation_candidates.append(('google_translate', google_translation, 0.70))
            except Exception as e:
                logger.warning(f"Google Translate failed: {e}")
        
        # Select best translation
        if translation_candidates:
            # Sort by confidence and choose the best
            translation_candidates.sort(key=lambda x: x[2], reverse=True)
            method, translation, method_confidence = translation_candidates[0]
            
            # Validate translation quality
            validation_score = self._validate_translation(cleaned_text, translation, field_type)
            final_confidence = min(confidence * method_confidence * validation_score, 1.0)
            
            return {
                'original': text,
                'translated': translation,
                'translation_method': method,
                'translation_confidence': final_confidence,
                'field_type': field_type,
                'alternative_translations': [
                    {'method': m, 'translation': t, 'confidence': c} 
                    for m, t, c in translation_candidates[1:3]  # Top 3 alternatives
                ]
            }
        else:
            # No translation available
            logger.warning(f"No translation available for: {text}")
            return {
                'original': text,
                'translated': text,
                'translation_method': 'failed',
                'translation_confidence': 0.0,
                'field_type': field_type
            }
    
    def _is_arabic_text(self, text: str) -> bool:
        """Check if text contains Arabic characters"""
        arabic_chars = sum(1 for c in text if '\u0600' <= c <= '\u06FF' or 
                          '\u0750' <= c <= '\u077F' or 
                          '\u08A0' <= c <= '\u08FF')
        return arabic_chars > 0
    
    def _validate_translation(self, original: str, translated: str, field_type: str) -> float:
        """Validate translation quality"""
        if not translated or translated == original:
            return 0.5
        
        # Basic validation
        score = 0.7  # Base score
        
        # Field-specific validation
        if field_type in ['first_name', 'last_name']:
            # Names should be capitalized and contain only letters
            if translated[0].isupper() and translated.replace(' ', '').isalpha():
                score += 0.2
        
        elif field_type == 'birth_place':
            # Places should be capitalized
            if translated[0].isupper():
                score += 0.2
        
        elif field_type == 'nationality':
            # Should contain nationality keywords
            if any(word in translated.lower() for word in ['tunisien', 'tunisienne', 'français', 'française']):
                score += 0.3
        
        # Length validation (translation shouldn't be too different in length)
        length_ratio = len(translated) / max(len(original), 1)
        if 0.5 <= length_ratio <= 2.0:
            score += 0.1
        
        return min(score, 1.0)
    
    def translate_id_card_data(self, parsed_data: Dict) -> Dict:
        """
        Enhanced translation of all extracted ID card data
        """
        logger.info("Starting enhanced translation of ID card data")
        
        translated_data = {
            'original_data': {},
            'translated_data': {},
            'translation_metadata': {
                'translation_timestamp': None,
                'translation_methods_used': [],
                'overall_translation_confidence': 0.0,
                'model_performance': {}
            }
        }
        
        # Fields that need translation
        translatable_fields = ['first_name', 'last_name', 'birth_place', 'nationality', 'address']
        
        total_confidence = 0.0
        translated_count = 0
        method_counts = {}
        
        for field in translatable_fields:
            if field in parsed_data and parsed_data[field] is not None:
                field_data = parsed_data[field]
                if isinstance(field_data, dict) and 'value' in field_data:
                    original_value = field_data['value']
                    ocr_confidence = field_data.get('confidence', 1.0)
                    
                    # Translate the field
                    translation_result = self.translate_field(original_value, field, ocr_confidence)
                    
                    # Store results
                    translated_data['original_data'][field] = field_data
                    translated_data['translated_data'][field] = {
                        **field_data,
                        'translated_value': translation_result['translated'],
                        'translation_info': translation_result
                    }
                    
                    # Track translation methods
                    method = translation_result['translation_method']
                    if method not in translated_data['translation_metadata']['translation_methods_used']:
                        translated_data['translation_metadata']['translation_methods_used'].append(method)
                    
                    method_counts[method] = method_counts.get(method, 0) + 1
                    
                    # Calculate overall confidence
                    total_confidence += translation_result['translation_confidence']
                    translated_count += 1
        
        # Copy non-translatable fields
        non_translatable_fields = ['document_type', 'card_number', 'birth_date', 'extraction_metadata']
        for field in non_translatable_fields:
            if field in parsed_data:
                translated_data['original_data'][field] = parsed_data[field]
                translated_data['translated_data'][field] = parsed_data[field]
        
        # Calculate overall translation confidence
        if translated_count > 0:
            translated_data['translation_metadata']['overall_translation_confidence'] = total_confidence / translated_count
        
        # Store model performance statistics
        translated_data['translation_metadata']['model_performance'] = method_counts
        
        from datetime import datetime
        translated_data['translation_metadata']['translation_timestamp'] = datetime.now().isoformat()
        
        logger.info(f"Enhanced translation completed. Overall confidence: "
                   f"{translated_data['translation_metadata']['overall_translation_confidence']:.2f}")
        logger.info(f"Methods used: {method_counts}")
        
        return translated_data
