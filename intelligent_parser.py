"""
Intelligent Parser with advanced regex patterns and AI-powered validation
Uses sophisticated pattern matching and machine learning for better extraction
"""

import re
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import unicodedata
import difflib

try:
    from transformers import pipeline, AutoTokenizer, AutoModelForTokenClassification
    NER_AVAILABLE = True
except ImportError:
    NER_AVAILABLE = False
    logging.warning("Transformers not available for NER")

from config import ID_CARD_PATTERNS, QUALITY_THRESHOLDS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IntelligentIDCardParser:
    """Intelligent parser with advanced regex and AI validation"""
    
    def __init__(self):
        """Initialize the intelligent parser"""
        self.patterns = ID_CARD_PATTERNS
        self.quality_thresholds = QUALITY_THRESHOLDS
        
        # Enhanced Arabic to Latin number mapping
        self.arabic_to_latin_numbers = {
            '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
            '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9',
            '۰': '0', '۱': '1', '۲': '2', '۳': '3', '۴': '4',
            '۵': '5', '۶': '6', '۷': '7', '۸': '8', '۹': '9'
        }
        
        # Advanced regex patterns for Tunisian ID cards
        self.advanced_patterns = {
            'card_number': [
                r'(?:رقم البطاقة|N°|Numéro|بطاقة رقم)[:\s]*(\d{8})',
                r'(?:^|\s)(\d{8})(?:\s|$)',  # Standalone 8-digit number
                r'(?:ID|CIN)[:\s]*(\d{8})',
                r'(\d{2}\s?\d{3}\s?\d{3})',  # Formatted number
            ],
            'first_name': [
                r'(?:الاسم|Prénom|اسم|الاسم الشخصي)[:\s]*([أ-ي\s]{2,30})',
                r'(?:^|\n)([أ-ي]{2,15})\s+(?:بن|ابن|ولد)',  # Name before "ben"
                r'(?:Mr|M\.)\s*([A-Z][a-z]{1,15})',
                r'(?:^|\s)([أ-ي]{3,15})(?:\s+[أ-ي]{3,15})*(?=\s*(?:بن|ابن))',
            ],
            'last_name': [
                r'(?:اللقب|Nom|لقب|اسم العائلة)[:\s]*([أ-ي\s]{2,30})',
                r'(?:بن|ابن|ولد)\s+([أ-ي\s]{2,30})',
                r'(?:Ben|Ibn)\s+([A-Z][a-z\s]{1,25})',
                r'(?:^|\s)([أ-ي]{2,20})(?:\s|$)(?=.*(?:تونس|صفاقس|سوسة))',  # Name near city
            ],
            'birth_date': [
                r'(?:تاريخ الولادة|Date de naissance|ولد في|né le)[:\s]*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{4})',
                r'(?:^|\s)(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{4})(?:\s|$)',
                r'(\d{4}[\/\-\.]\d{1,2}[\/\-\.]\d{1,2})',  # Year first
                r'(\d{1,2}\s+\w+\s+\d{4})',  # Day Month Year in text
            ],
            'birth_place': [
                r'(?:مكان الولادة|Lieu de naissance|محل الولادة|ولد في|né à)[:\s]*([أ-ي\s]{2,25})',
                r'(?:^|\s)(تونس|صفاقس|سوسة|القيروان|بنزرت|قابس|أريانة|منوبة|بن عروس|نابل)(?:\s|$)',
                r'(?:^|\s)(Tunis|Sfax|Sousse|Kairouan|Bizerte|Gabès|Ariana)(?:\s|$)',
            ],
            'nationality': [
                r'(?:الجنسية|Nationalité|جنسية)[:\s]*([أ-ي\s]{2,15})',
                r'(?:^|\s)(تونسي|تونسية|Tunisien|Tunisienne)(?:\s|$)',
                r'(?:République|الجمهورية)\s*(?:Tunisienne|التونسية)',
            ],
            'address': [
                r'(?:العنوان|Adresse|عنوان|محل الإقامة)[:\s]*([أ-ي\s\d\،\.\-]{5,50})',
                r'(?:شارع|نهج|طريق|Avenue|Rue|Street)\s*([أ-ي\s\d\،\.\-]{5,40})',
                r'(?:^|\s)(\d+\s*[أ-ي\s\d\،\.\-]{5,40})(?:\s|$)',  # Address starting with number
            ]
        }
        
        # Initialize NER model if available
        self.ner_pipeline = None
        if NER_AVAILABLE:
            try:
                self.ner_pipeline = pipeline("ner", 
                                           model="CAMeL-Lab/bert-base-arabic-camelbert-msa-ner",
                                           aggregation_strategy="simple")
                logger.info("Arabic NER model initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize NER model: {e}")
        
        # Tunisian cities and common names for validation
        self.tunisian_cities = {
            'تونس', 'صفاقس', 'سوسة', 'القيروان', 'بنزرت', 'قابس', 'أريانة', 'منوبة', 
            'بن عروس', 'نابل', 'زغوان', 'باجة', 'جندوبة', 'الكاف', 'سليانة', 
            'القصرين', 'سيدي بوزيد', 'قفصة', 'توزر', 'قبلي', 'تطاوين', 'مدنين',
            'Tunis', 'Sfax', 'Sousse', 'Kairouan', 'Bizerte', 'Gabès', 'Ariana'
        }
        
        self.common_arabic_names = {
            'محمد', 'أحمد', 'علي', 'حسن', 'حسين', 'عبد الله', 'عبد الرحمن', 'عبد العزيز',
            'فاطمة', 'عائشة', 'خديجة', 'زينب', 'مريم', 'سارة', 'ليلى', 'نور', 'أمينة'
        }
    
    def normalize_arabic_text(self, text: str) -> str:
        """Enhanced Arabic text normalization"""
        if not text:
            return ""
        
        # Remove diacritics
        text = ''.join(c for c in unicodedata.normalize('NFD', text) 
                      if unicodedata.category(c) != 'Mn')
        
        # Convert Arabic numbers to Latin
        for arabic, latin in self.arabic_to_latin_numbers.items():
            text = text.replace(arabic, latin)
        
        # Normalize Arabic characters
        text = text.replace('ة', 'ه')  # Taa marbuta to haa
        text = text.replace('ى', 'ي')  # Alif maksura to yaa
        text = text.replace('إ', 'ا')  # Alif with hamza to alif
        text = text.replace('أ', 'ا')  # Alif with hamza to alif
        text = text.replace('آ', 'ا')  # Alif madda to alif
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def extract_with_advanced_regex(self, ocr_results: List[Dict], field_type: str) -> List[Dict]:
        """Extract field using advanced regex patterns"""
        patterns = self.advanced_patterns.get(field_type, [])
        candidates = []
        
        # Combine all OCR text for context-aware extraction
        full_text = ' '.join([result['text'] for result in ocr_results])
        normalized_full_text = self.normalize_arabic_text(full_text)
        
        # Try patterns on individual results
        for result in ocr_results:
            text = result['text']
            normalized_text = self.normalize_arabic_text(text)
            
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.UNICODE | re.IGNORECASE)
                for match in matches:
                    value = match.group(1) if match.groups() else match.group(0)
                    value = value.strip()
                    
                    if self._validate_field_value(value, field_type):
                        candidates.append({
                            'value': value,
                            'confidence': result['confidence'],
                            'bbox': result['bbox'],
                            'source_text': result['text'],
                            'pattern_match': True,
                            'validation_score': self._calculate_validation_score(value, field_type)
                        })
                
                # Also try on normalized text
                matches = re.finditer(pattern, normalized_text, re.UNICODE | re.IGNORECASE)
                for match in matches:
                    value = match.group(1) if match.groups() else match.group(0)
                    value = value.strip()
                    
                    if self._validate_field_value(value, field_type):
                        candidates.append({
                            'value': value,
                            'confidence': result['confidence'] * 0.9,  # Slightly lower for normalized
                            'bbox': result['bbox'],
                            'source_text': result['text'],
                            'pattern_match': True,
                            'validation_score': self._calculate_validation_score(value, field_type)
                        })
        
        # Try patterns on full combined text
        for pattern in patterns:
            matches = re.finditer(pattern, normalized_full_text, re.UNICODE | re.IGNORECASE)
            for match in matches:
                value = match.group(1) if match.groups() else match.group(0)
                value = value.strip()
                
                if self._validate_field_value(value, field_type):
                    # Find the source OCR result
                    best_source = max(ocr_results, key=lambda x: x['confidence'])
                    candidates.append({
                        'value': value,
                        'confidence': best_source['confidence'] * 0.8,  # Lower for combined text
                        'bbox': best_source['bbox'],
                        'source_text': 'combined_text',
                        'pattern_match': True,
                        'validation_score': self._calculate_validation_score(value, field_type)
                    })
        
        return candidates
    
    def extract_with_ner(self, ocr_results: List[Dict]) -> Dict[str, List[Dict]]:
        """Extract entities using Named Entity Recognition"""
        if not self.ner_pipeline:
            return {}
        
        ner_results = {'first_name': [], 'last_name': [], 'birth_place': []}
        
        try:
            # Combine text for NER
            full_text = ' '.join([result['text'] for result in ocr_results if result['text']])
            
            if not full_text.strip():
                return ner_results
            
            entities = self.ner_pipeline(full_text)
            
            for entity in entities:
                entity_type = entity['entity_group'].upper()
                entity_text = entity['word'].strip()
                confidence = entity['score']
                
                # Map NER labels to our fields
                if entity_type in ['PER', 'PERSON']:
                    # Try to determine if it's first or last name
                    if any(name in entity_text for name in self.common_arabic_names):
                        ner_results['first_name'].append({
                            'value': entity_text,
                            'confidence': confidence,
                            'bbox': [0, 0, 100, 100],  # Placeholder bbox
                            'source_text': 'ner_extraction',
                            'ner_match': True,
                            'validation_score': self._calculate_validation_score(entity_text, 'first_name')
                        })
                    else:
                        ner_results['last_name'].append({
                            'value': entity_text,
                            'confidence': confidence,
                            'bbox': [0, 0, 100, 100],
                            'source_text': 'ner_extraction',
                            'ner_match': True,
                            'validation_score': self._calculate_validation_score(entity_text, 'last_name')
                        })
                
                elif entity_type in ['LOC', 'LOCATION']:
                    if entity_text in self.tunisian_cities:
                        ner_results['birth_place'].append({
                            'value': entity_text,
                            'confidence': confidence,
                            'bbox': [0, 0, 100, 100],
                            'source_text': 'ner_extraction',
                            'ner_match': True,
                            'validation_score': self._calculate_validation_score(entity_text, 'birth_place')
                        })
        
        except Exception as e:
            logger.warning(f"NER extraction failed: {e}")
        
        return ner_results
    
    def _validate_field_value(self, value: str, field_type: str) -> bool:
        """Validate extracted field value"""
        if not value or len(value.strip()) < 1:
            return False
        
        value = value.strip()
        
        if field_type == 'card_number':
            return re.match(r'^\d{7,9}$', value) is not None
        
        elif field_type in ['first_name', 'last_name']:
            # Check if it's mostly letters
            letter_count = sum(1 for c in value if c.isalpha() or c in 'أبتثجحخدذرزسشصضطظعغفقكلمنهويءآإؤئةى')
            return letter_count >= len(value) * 0.7 and len(value) >= 2
        
        elif field_type == 'birth_date':
            return self._is_valid_date(value)
        
        elif field_type == 'birth_place':
            return len(value) >= 2 and (value in self.tunisian_cities or 
                                      any(city in value for city in self.tunisian_cities))
        
        elif field_type == 'nationality':
            nationality_keywords = ['تونسي', 'تونسية', 'Tunisien', 'Tunisienne', 'التونسية']
            return any(keyword in value for keyword in nationality_keywords)
        
        elif field_type == 'address':
            return len(value) >= 5
        
        return True
    
    def _calculate_validation_score(self, value: str, field_type: str) -> float:
        """Calculate validation score for a field value"""
        score = 0.5  # Base score
        
        if field_type == 'card_number':
            if re.match(r'^\d{8}$', value):
                score = 1.0
            elif re.match(r'^\d{7,9}$', value):
                score = 0.8
        
        elif field_type in ['first_name', 'last_name']:
            if value in self.common_arabic_names:
                score = 1.0
            elif len(value) >= 3 and all(c.isalpha() or c in 'أبتثجحخدذرزسشصضطظعغفقكلمنهويءآإؤئةى ' for c in value):
                score = 0.8
        
        elif field_type == 'birth_place':
            if value in self.tunisian_cities:
                score = 1.0
            elif any(city in value for city in self.tunisian_cities):
                score = 0.8
        
        elif field_type == 'nationality':
            if value in ['تونسي', 'تونسية', 'Tunisien', 'Tunisienne']:
                score = 1.0
        
        return score
    
    def _is_valid_date(self, date_str: str) -> bool:
        """Validate if a string is a valid date"""
        date_formats = ['%d/%m/%Y', '%d-%m-%Y', '%d.%m.%Y', '%Y/%m/%d', '%Y-%m-%d']
        
        for fmt in date_formats:
            try:
                date_obj = datetime.strptime(date_str, fmt)
                # Check if date is reasonable (between 1900 and current year)
                if 1900 <= date_obj.year <= datetime.now().year:
                    return True
            except ValueError:
                continue
        return False
    
    def select_best_candidate(self, candidates: List[Dict]) -> Optional[Dict]:
        """Select the best candidate from multiple extractions"""
        if not candidates:
            return None
        
        # Score each candidate
        for candidate in candidates:
            score = 0.0
            
            # OCR confidence weight
            score += candidate['confidence'] * 0.4
            
            # Validation score weight
            score += candidate.get('validation_score', 0.5) * 0.3
            
            # Pattern match bonus
            if candidate.get('pattern_match', False):
                score += 0.2
            
            # NER match bonus
            if candidate.get('ner_match', False):
                score += 0.1
            
            candidate['final_score'] = score
        
        # Return candidate with highest score
        best_candidate = max(candidates, key=lambda x: x['final_score'])
        return best_candidate
    
    def parse_id_card(self, ocr_results: List[Dict]) -> Dict:
        """
        Main intelligent parsing method
        """
        logger.info("Starting intelligent ID card parsing")
        
        parsed_data = {
            'document_type': None,
            'card_number': None,
            'first_name': None,
            'last_name': None,
            'birth_date': None,
            'birth_place': None,
            'nationality': None,
            'address': None,
            'extraction_metadata': {
                'total_ocr_elements': len(ocr_results),
                'parsing_timestamp': datetime.now().isoformat(),
                'confidence_scores': {},
                'raw_ocr_texts': [r['text'] for r in ocr_results],
                'extraction_methods': []
            }
        }
        
        # Extract with advanced regex
        for field_type in ['card_number', 'first_name', 'last_name', 'birth_date', 'birth_place', 'nationality', 'address']:
            regex_candidates = self.extract_with_advanced_regex(ocr_results, field_type)
            
            # Extract with NER for applicable fields
            ner_candidates = []
            if field_type in ['first_name', 'last_name', 'birth_place']:
                ner_results = self.extract_with_ner(ocr_results)
                ner_candidates = ner_results.get(field_type, [])
            
            # Combine all candidates
            all_candidates = regex_candidates + ner_candidates
            
            # Select best candidate
            best_candidate = self.select_best_candidate(all_candidates)
            
            if best_candidate:
                parsed_data[field_type] = best_candidate
                parsed_data['extraction_metadata']['confidence_scores'][field_type] = best_candidate['final_score']
                
                # Track extraction method
                method = 'regex'
                if best_candidate.get('ner_match'):
                    method = 'ner'
                elif best_candidate.get('pattern_match'):
                    method = 'advanced_regex'
                
                if method not in parsed_data['extraction_metadata']['extraction_methods']:
                    parsed_data['extraction_metadata']['extraction_methods'].append(method)
        
        # Extract document type
        document_type_candidates = self.extract_with_advanced_regex(ocr_results, 'document_type')
        if document_type_candidates:
            parsed_data['document_type'] = {
                'value': 'Tunisian ID Card',
                'confidence': max(c['confidence'] for c in document_type_candidates),
                'bbox': document_type_candidates[0]['bbox'],
                'source_text': document_type_candidates[0]['source_text']
            }
        
        # Calculate overall quality score
        required_fields = ['card_number', 'first_name', 'last_name']
        found_required = sum(1 for field in required_fields if parsed_data[field] is not None)
        quality_score = found_required / len(required_fields)
        
        # Bonus for additional fields
        optional_fields = ['birth_date', 'birth_place', 'nationality', 'address']
        found_optional = sum(1 for field in optional_fields if parsed_data[field] is not None)
        quality_bonus = (found_optional / len(optional_fields)) * 0.3
        
        parsed_data['extraction_metadata']['quality_score'] = min(1.0, quality_score + quality_bonus)
        
        logger.info(f"Intelligent parsing completed. Quality score: {parsed_data['extraction_metadata']['quality_score']:.2f}")
        return parsed_data
